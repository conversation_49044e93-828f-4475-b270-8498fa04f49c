using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PUBGMESP
{
    internal class Settings
    {
        public static bool PlayerESP = true;
        public static bool PlayerBone = true;
        public static bool PlayerBox = false;
        public static bool Player3dBox = false;
        public static bool PlayerLines = true;
        public static bool PlayerHealth = true;
        public static bool ItemESP = false;
        public static bool VehicleESP = false;
        public static bool ShowMenu = true;
        public static bool Headmode = false;
        public static bool Waterbox = false;
        public static bool playername = true;
        public static bool ShowLootItem = true;
        public static bool FastLanding = false;
       // public static bool Fastrun = false;
        public static bool NoRecoil = false;
        public static bool SpeedCar = false;
        public static bool INSTANTHIT = false;
        public static bool smallpointer = true;


        // aimbot
        public static bool aimEnabled = false;
        public static bool bDrawFow = false;
        public static int bSmooth = 11;
        public static int bFovInt = 4;
        public static int bPredict = 1;
        public static int bYAxis = 2;
        public static int bYAxis1 = 5;
        public static int bYAxis2 = 7;
        public static int bYAxis3 = 8;
        public static int bYAxis4 = 9;
        public static int bYAxis5 = 10;
        public static int bYAxis6 = 11;
        public static int bYAxis7 = 12;
        public static int bYAxis8 = 13;
        public static int bYAxis9 = 14;
        public static int bYAxis10 = 15;
        public static int bYAxis11 = 16;
        public static int bYAxis12 = 17;
        public static int bYAxis13 = 18;
        public static int bYAxis14 = 19;
        public static int bYAxis15 = 20;
        public static int bYAxis16 = 21;
        public static int bYAxis17 = 22;
        public static int bYAxis18 = 23;
        public static int bYAxis19 = 24;
        public static int bYAxis20 = 25;
        public static int bYAxis21 = 26;
        public static int bYAxis22 = 27;
        public static int bYAxis23 = 28;
        public static int bYAxis24 = 29;
        public static int bYAxis25 = 30;
        public static int bYAxis26 = 31;
        public static int bYAxis27 = 32;
        public static int bYAxis28 = 33;
        public static int bYAxis29 = 34;
        public static int bYAxis30 = 35;
        public static int bYAxis31 = 36;
        public static int bYAxis32 = 37;
        public static int bYAxis33 = 38;
        public static int bYAxis34 = 39;
        public static int bYAxis35 = 40;
        public static int bYAxis36 = 41;
        public static int bYAxis37 = 42;
        public static int bYAxis38 = 43;
        public static int bYAxis39 = 44;
        public static int bYAxis40 = 45;
        public static int bYAxis41 = 46;
        public static int bYAxis42 = 47;
        public static int bYAxis43 = 48;
        public static int bYAxis44 = 49;
        public static int bYAxis45 = 50;
        public static int bYAxis46 = 51;
        public static int bYAxis47 = 52;
        public static int bYAxis48 = 53;
        public static int bYAxis49 = 54;
        public static int bYAxis50 = 55;
        public static int bYAxis51 = 56;
        public static int bYAxis52 = 57;
        public static int bYAxis53 = 58;
        public static int bYAxis54 = 59;
        public static int bYAxis55 = 60;
        public static int bYAxis56 = 61;
        public static int bAimKeyINT = 1;
        public static string[] aimkeys = { "CAPSLOCK", "LBUTTON", "RBUTTON", "LSHIFT", "V", "E", "Q" };
        public static System.Windows.Forms.Keys[] bAimKeys = new System.Windows.Forms.Keys[] { System.Windows.Forms.Keys.CapsLock, System.Windows.Forms.Keys.LButton, System.Windows.Forms.Keys.RButton, System.Windows.Forms.Keys.LShiftKey, System.Windows.Forms.Keys.V, System.Windows.Forms.Keys.E, System.Windows.Forms.Keys.Q };
        public static float[] bFovArray = new float[]
        {
            60f,
            90f,
            120f,
            160f,
            300f,
            300f
        };

    }
}



