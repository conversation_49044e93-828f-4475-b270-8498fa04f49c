<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

	<!--
	When using Sdk-style projects and default embedded resource items are enabled, automatically
	add the XAML files and fix up item metadata to pair them with code files.
	This is in the props file, not the targets file, so that projects can remove/update these items.
	The default value for EnableDefaultXamlItems is set in the targets, which works because
	the property evaluation pass comes before the item evaluation pass.

	The actual item groups are in a separate conditionally-imported file as they use constructs that
	are not compatible with older MSBuild versions.
	-->
	<Import Project="$(MSBuildThisFileDirectory)Xamarin.Forms.DefaultItems.props" Condition="'$(MSBuildSDKsPath)'!=''" />

</Project>
