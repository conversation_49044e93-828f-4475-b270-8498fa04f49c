﻿"DeployProject"
{
"VSVersion" = "3:800"
"ProjectType" = "8:{978C614F-708E-4E1A-B201-565925725DBA}"
"IsWebType" = "8:FALSE"
"ProjectName" = "8:JOY"
"LanguageId" = "3:1033"
"CodePage" = "3:1252"
"UILanguageId" = "3:1033"
"SccProjectName" = "8:"
"SccLocalPath" = "8:"
"SccAuxPath" = "8:"
"SccProvider" = "8:"
    "Hierarchy"
    {
        "Entry"
        {
        "MsmKey" = "8:_01E7DA0405145C166BD196B36BE68411"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_01E7DA0405145C166BD196B36BE68411"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_01E7DA0405145C166BD196B36BE68411"
        "OwnerKey" = "8:_7D5AED969856061FB3DBB9885091DD30"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_02515BD70C02D89E680D427047428AE1"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0521DFDB0BA2B97655D6B6C1E1EA9877"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_05AF8E7CCC983CDAA1F6F276F952F030"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_05D90BD716E44CB76FB1713306D5BE97"
        "OwnerKey" = "8:_4467D86EC33DC598842BC897FDF65EA7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_05D90BD716E44CB76FB1713306D5BE97"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_05D90BD716E44CB76FB1713306D5BE97"
        "OwnerKey" = "8:_FEE158D67CE54365E0858AA479E4AF4A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0C0A13A67B2C675C56DA2F113D493643"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_0F6CC24C1F351E70F21634A04933D6C2"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_121F92DCBE07B0058402E414A64189CF"
        "OwnerKey" = "8:_FEE158D67CE54365E0858AA479E4AF4A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_121F92DCBE07B0058402E414A64189CF"
        "OwnerKey" = "8:_6FEC36CE23840B7AE04E2A082B53981C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_121F92DCBE07B0058402E414A64189CF"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_121F92DCBE07B0058402E414A64189CF"
        "OwnerKey" = "8:_05D90BD716E44CB76FB1713306D5BE97"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_121F92DCBE07B0058402E414A64189CF"
        "OwnerKey" = "8:_4467D86EC33DC598842BC897FDF65EA7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_122A3969849EACDE8C01155653D949DF"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1410BF615C34D3C9C84E015AD0BBCEF6"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1437729AD3038E139B3142CC334B92E6"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1437729AD3038E139B3142CC334B92E6"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_163864923D170DBAD2B11A6B9ACB67E9"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1A4FBFF8BF375A542B8E19BD244A13A7"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1A5ED45D84425BF88AEE2C8B1EFBF670"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1A5ED45D84425BF88AEE2C8B1EFBF670"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1AA4DA6CB720DF1811DF7A89E507891A"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1AB2BFE8140D2D490B1A70BD653B65CB"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1B55927CF5A2205C039658DEDB0DA5A2"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_1DCC264D90984A9ECA1BB96446BB2056"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_244221982303B8007636F85CB3DE2D32"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_290AE77B93B0C97156B0DC6DFB563A58"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2B36C6015A9EE5DBEE4B780D3E97F079"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2BE2B7FFEEB27167FD54D5235F985673"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2E2EAA1F22B6745155FC98C537BB7EF7"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2F60CE4A474335E4F755F83BC7AF5463"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_2FF4CBC7171157C671DEB982FB2590D1"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_30C2EBCCC9EA55BB02305F38EED591BE"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3260895E63550ABD91CF963566F43136"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_35C8E32BE3BFF001B6D257E0924F7B19"
        "OwnerKey" = "8:_7D5AED969856061FB3DBB9885091DD30"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_370892C097F3F73C183C2B55006541D6"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3A2A4C7095359B564895CAA15FC3C83D"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3A65B2F8A62C663E667E2015527BD46E"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3A65B2F8A62C663E667E2015527BD46E"
        "OwnerKey" = "8:_9D75182B096F27AFC396BE6BC397D158"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3B8AB27C741625E1CE54376599F6BA1D"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3DAC407B0CF2BA259D3578D5A2D0028D"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_3F9B34F45C576C98D45173B181DF633A"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_425A36504A6828631091637046658E47"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4294E6EEC788C3DEE1EF1EE90D1EEA24"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_43D872F268806F29B3A0033DA7C24C09"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4467D86EC33DC598842BC897FDF65EA7"
        "OwnerKey" = "8:_FEE158D67CE54365E0858AA479E4AF4A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4467D86EC33DC598842BC897FDF65EA7"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "OwnerKey" = "8:_UNDEFINED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45764A7EBDBE7F8937DC4A2D49365ED4"
        "OwnerKey" = "8:_3A65B2F8A62C663E667E2015527BD46E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_45764A7EBDBE7F8937DC4A2D49365ED4"
        "OwnerKey" = "8:_52B22BAB453ECF56A867209CB96F7CED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_49D5A849F7D8A9DB2B1EB306F4F21CBB"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4C853350C7789E9D84C60FE3240C11F0"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "OwnerKey" = "8:_9D75182B096F27AFC396BE6BC397D158"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4E024D6259FF671DCA757CDCEB6A6099"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4EB37E11C4E392E17AF881E14CE5E58C"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4FA228E1D7CD6EDD504F3F39A54A3AA9"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_4FFB44359A3B0E987E9322181DA789C4"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_50494896C6B7942A0981F05C0B3D17D6"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_50C1C497D2DFE25F004301639713C44E"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_52B22BAB453ECF56A867209CB96F7CED"
        "OwnerKey" = "8:_F0B9B5CEB7A5CF3438F97A69894CF59F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_52EECE1ED22627FD22E268EA1BEB9FA4"
        "OwnerKey" = "8:_A4FD7FF240232C28833686D6605BE802"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_52EECE1ED22627FD22E268EA1BEB9FA4"
        "OwnerKey" = "8:_3A65B2F8A62C663E667E2015527BD46E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_578B38C765A941B123BE852D48340E99"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5861429DB5455D194632214DB099EF42"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_587B09A3D3110602EB54F83438554349"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5A376FCFD60F88E1269AF207B9D17D8A"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5DF23DB30055BB5D0492B8431A8AD498"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_5DF23DB30055BB5D0492B8431A8AD498"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_637742C64A0AD86931D4008FF5856DDC"
        "OwnerKey" = "8:_71B44FB7BB0F87798964DC94B89EB61B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_637742C64A0AD86931D4008FF5856DDC"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6429C4C71FC33109A9EDD4A5700D82AD"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_671CDE26862A178FB690A9A1C7A89FFF"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_67B2B98EA3CFE87AF519D1294837B6EB"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6ADE07DC1D7A44FA68B838614F057967"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6D4D9AE79192537D1C62F37DB44A3327"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6E0D22B3835C10988ABDA9696E78288D"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6FAACBE933ABC71A9EA2C0B07CE907A8"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_6FEC36CE23840B7AE04E2A082B53981C"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7076989A598D73D988CB56CB2BBFE590"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_71B44FB7BB0F87798964DC94B89EB61B"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7542DEB7D5DC191B5FCB5BA77B34170F"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7747EE074B5F3B1EBE70D9954F24C469"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7775201DA141306EA9DCCA685D5360EB"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7775201DA141306EA9DCCA685D5360EB"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_785D75DD8EB353EEBB0280AC6F2D8F2E"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7939658D154EA0604AEF6D9426796236"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_794464E7967E653311BF715D99169EA3"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_794464E7967E653311BF715D99169EA3"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7D5AED969856061FB3DBB9885091DD30"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_7D5AED969856061FB3DBB9885091DD30"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_833DD21D2AA70449BA759D9249A2971A"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_89C9DA0672EB9718A517098B4DA23E7F"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8A6250FE037075A5D5A1BD01EDF55E6C"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8B5638B87DF0DF5232B1D506C7AD8AA6"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_8F5E03BC65509F9E3E398AEED92114EA"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_915381240883D9E2AAE24999DDEEBD57"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_954D407CAAD57516740BDE7350CC72ED"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_96ACBD054FE73116E89D8CFC37BBEFB8"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_96E7546D71956346FC6EEBBB1A432B77"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_97A992293FF9B9A879C10D28B64E7D37"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9A2E06F3E0B7683F2A6CFD87BBDE3848"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9D75182B096F27AFC396BE6BC397D158"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9E53ADBDA5349368D72FFF36E6370D6B"
        "OwnerKey" = "8:_3A65B2F8A62C663E667E2015527BD46E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9FD447983DD7E1575C65C6DAEF121524"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_9FF64FD7B43E528DFF1AE401583E91B4"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A0FA8D2CBAE77F6B4C5A3231623B2771"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A2982F13E183DA52C5CBB8D1BB7C1A47"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A484E235D28E37FE0708AD6CD36353A9"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A4FD7FF240232C28833686D6605BE802"
        "OwnerKey" = "8:_3A65B2F8A62C663E667E2015527BD46E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A4FD7FF240232C28833686D6605BE802"
        "OwnerKey" = "8:_BA947C97364E3C01A5D68AC444C75ED8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A58358AD0913F984E89FB196A683E490"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A5877197275074769BFEE5E40B034142"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_A6FC4E907839CCAFF20593CD9A6D2F02"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AB54C2D4993F5793B384E1CFD3C6D6EE"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AB54C2D4993F5793B384E1CFD3C6D6EE"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_ACEF7F113BF7FD71C9B3E57FA01B814C"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AEFA980BC329863B0614B407F66C52B0"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AFBC7E7BF4C18126774926259B0E3425"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_AFF7581BD9D784C95295D67D33FE7186"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0B7A91FB07B2E6033BBA473C033D3C8"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0B7A91FB07B2E6033BBA473C033D3C8"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B0DAEB9E903BCF31818C3958A191FF3A"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B1DEF0F5B8E69CB5F096ADD785002E07"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B9CC798D2A0250AF834483C1B92B3F9C"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_B9ED22CAC73D9163773CB1D65DD6ACA1"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_BA947C97364E3C01A5D68AC444C75ED8"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C06D7742C83385E6850DBB2C4C924C11"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C177140B344CED9E0E47B3701347555B"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C2DE2FBBA9DAB06C0577DBBCF4926358"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C4D556C2B8910F9AC4BEF606843C84C6"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C54B2F804682DF339389A3C2DD40377C"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C54B2F804682DF339389A3C2DD40377C"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C59EFC5B6729D91776B3F0289D939AF1"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_C9E6868BAB58440582321B5162CB30D5"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CB6D921CCD2BDFBE468FACCD9602C357"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CB7887B831473B396A59E6115D2B843D"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CCDA3E1E6E5B8588A367274ABCC1A335"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CD02533B06AC19E1A80E92E25ED26BDD"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CE3CC3B21A8CE50D6E5FB0B18345E00A"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CE3CC3B21A8CE50D6E5FB0B18345E00A"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_CFF3E59DA41AD34E2FBA4CF407FFAFE6"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D338FE4406FAE5E496A913A834B18EDA"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_D8C401EBD38AD3DE4582E00A8E0BFB0F"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DB916CBDF914C2BF2D82CC7F1F1EA563"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DC8004AC135904E56085322307851BAF"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_DF87D965968AEE951B9DF14653E13A74"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E3CD88AB6F8A3B28B9395A678E2BA886"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E4D00C202B558BE30BF6A291D6CCF8B2"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E5966CDA4988BA92BBF9127214BB9F93"
        "OwnerKey" = "8:_F687DEFA2759F19EE0CA0C773C9A28E1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E5966CDA4988BA92BBF9127214BB9F93"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E5966CDA4988BA92BBF9127214BB9F93"
        "OwnerKey" = "8:_71B44FB7BB0F87798964DC94B89EB61B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_E673A41F0DA9F34CFA3993064821637A"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EA2F99B8DDB3A91714F583AF2F0BE2E2"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_ED8EBD605C990D7017A6C579302F74E3"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EDAE5995396C436BFA1194D0AEF66CEC"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_EF160C805523A42E0BBBE5838DB4A6A8"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F0B9B5CEB7A5CF3438F97A69894CF59F"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F1F4ADCEA929833BC0E2BD77985D98DC"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F36B12514A33C936C3AB887A26E02945"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F687DEFA2759F19EE0CA0C773C9A28E1"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_F83D575C3E81B8B958D7A64028A20D94"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FC7343B4C7B2E730AB64B00B5C59689A"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FC7343B4C7B2E730AB64B00B5C59689A"
        "OwnerKey" = "8:_BA710DA6F15000524F4F869E8A16372B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FED81A06B70597B23453141A39311922"
        "OwnerKey" = "8:_4D63B24A68881014A0EAA42598C21218"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FEE158D67CE54365E0858AA479E4AF4A"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FF6C3968935A714E6E4F741DCD3A60AB"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_FFA0D44FC402BE0AE2511B33F1F90A1D"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3DAC407B0CF2BA259D3578D5A2D0028D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_44F8CFDDBA3841E1B39703B35FF380B6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C4D556C2B8910F9AC4BEF606843C84C6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6FEC36CE23840B7AE04E2A082B53981C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FEE158D67CE54365E0858AA479E4AF4A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4467D86EC33DC598842BC897FDF65EA7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_05D90BD716E44CB76FB1713306D5BE97"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_121F92DCBE07B0058402E414A64189CF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_71B44FB7BB0F87798964DC94B89EB61B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_637742C64A0AD86931D4008FF5856DDC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F687DEFA2759F19EE0CA0C773C9A28E1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E5966CDA4988BA92BBF9127214BB9F93"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_05AF8E7CCC983CDAA1F6F276F952F030"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FF6C3968935A714E6E4F741DCD3A60AB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_35C8E32BE3BFF001B6D257E0924F7B19"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1437729AD3038E139B3142CC334B92E6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_43D872F268806F29B3A0033DA7C24C09"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FED81A06B70597B23453141A39311922"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_01E7DA0405145C166BD196B36BE68411"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_794464E7967E653311BF715D99169EA3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_AB54C2D4993F5793B384E1CFD3C6D6EE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B0B7A91FB07B2E6033BBA473C033D3C8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C177140B344CED9E0E47B3701347555B"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5DF23DB30055BB5D0492B8431A8AD498"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B0DAEB9E903BCF31818C3958A191FF3A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1A5ED45D84425BF88AEE2C8B1EFBF670"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CE3CC3B21A8CE50D6E5FB0B18345E00A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DB916CBDF914C2BF2D82CC7F1F1EA563"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C54B2F804682DF339389A3C2DD40377C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7775201DA141306EA9DCCA685D5360EB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FC7343B4C7B2E730AB64B00B5C59689A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_370892C097F3F73C183C2B55006541D6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0F6CC24C1F351E70F21634A04933D6C2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_89C9DA0672EB9718A517098B4DA23E7F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9FF64FD7B43E528DFF1AE401583E91B4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_EDAE5995396C436BFA1194D0AEF66CEC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7076989A598D73D988CB56CB2BBFE590"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_96ACBD054FE73116E89D8CFC37BBEFB8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6E0D22B3835C10988ABDA9696E78288D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_EF160C805523A42E0BBBE5838DB4A6A8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_49D5A849F7D8A9DB2B1EB306F4F21CBB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D8C401EBD38AD3DE4582E00A8E0BFB0F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E4D00C202B558BE30BF6A291D6CCF8B2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B9ED22CAC73D9163773CB1D65DD6ACA1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F36B12514A33C936C3AB887A26E02945"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_67B2B98EA3CFE87AF519D1294837B6EB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A0FA8D2CBAE77F6B4C5A3231623B2771"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CFF3E59DA41AD34E2FBA4CF407FFAFE6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_833DD21D2AA70449BA759D9249A2971A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F83D575C3E81B8B958D7A64028A20D94"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_954D407CAAD57516740BDE7350CC72ED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E3CD88AB6F8A3B28B9395A678E2BA886"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_587B09A3D3110602EB54F83438554349"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4FA228E1D7CD6EDD504F3F39A54A3AA9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3260895E63550ABD91CF963566F43136"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4294E6EEC788C3DEE1EF1EE90D1EEA24"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2E2EAA1F22B6745155FC98C537BB7EF7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0521DFDB0BA2B97655D6B6C1E1EA9877"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_122A3969849EACDE8C01155653D949DF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_E673A41F0DA9F34CFA3993064821637A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_290AE77B93B0C97156B0DC6DFB563A58"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_578B38C765A941B123BE852D48340E99"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B9CC798D2A0250AF834483C1B92B3F9C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_163864923D170DBAD2B11A6B9ACB67E9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_50C1C497D2DFE25F004301639713C44E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2B36C6015A9EE5DBEE4B780D3E97F079"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_FFA0D44FC402BE0AE2511B33F1F90A1D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9A2E06F3E0B7683F2A6CFD87BBDE3848"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2F60CE4A474335E4F755F83BC7AF5463"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_915381240883D9E2AAE24999DDEEBD57"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3A2A4C7095359B564895CAA15FC3C83D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CB7887B831473B396A59E6115D2B843D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6429C4C71FC33109A9EDD4A5700D82AD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8A6250FE037075A5D5A1BD01EDF55E6C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_ACEF7F113BF7FD71C9B3E57FA01B814C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_AFBC7E7BF4C18126774926259B0E3425"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3B8AB27C741625E1CE54376599F6BA1D"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6D4D9AE79192537D1C62F37DB44A3327"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F1F4ADCEA929833BC0E2BD77985D98DC"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7939658D154EA0604AEF6D9426796236"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_0C0A13A67B2C675C56DA2F113D493643"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2FF4CBC7171157C671DEB982FB2590D1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_244221982303B8007636F85CB3DE2D32"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_AEFA980BC329863B0614B407F66C52B0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8F5E03BC65509F9E3E398AEED92114EA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1410BF615C34D3C9C84E015AD0BBCEF6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7747EE074B5F3B1EBE70D9954F24C469"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5861429DB5455D194632214DB099EF42"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A5877197275074769BFEE5E40B034142"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_671CDE26862A178FB690A9A1C7A89FFF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A2982F13E183DA52C5CBB8D1BB7C1A47"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A6FC4E907839CCAFF20593CD9A6D2F02"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_EA2F99B8DDB3A91714F583AF2F0BE2E2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4E024D6259FF671DCA757CDCEB6A6099"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_F0B9B5CEB7A5CF3438F97A69894CF59F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_52B22BAB453ECF56A867209CB96F7CED"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C06D7742C83385E6850DBB2C4C924C11"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1DCC264D90984A9ECA1BB96446BB2056"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A484E235D28E37FE0708AD6CD36353A9"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_785D75DD8EB353EEBB0280AC6F2D8F2E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DF87D965968AEE951B9DF14653E13A74"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_AFF7581BD9D784C95295D67D33FE7186"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_D338FE4406FAE5E496A913A834B18EDA"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6ADE07DC1D7A44FA68B838614F057967"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CB6D921CCD2BDFBE468FACCD9602C357"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_5A376FCFD60F88E1269AF207B9D17D8A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_7542DEB7D5DC191B5FCB5BA77B34170F"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C59EFC5B6729D91776B3F0289D939AF1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_B1DEF0F5B8E69CB5F096ADD785002E07"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_BA947C97364E3C01A5D68AC444C75ED8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1AB2BFE8140D2D490B1A70BD653B65CB"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C2DE2FBBA9DAB06C0577DBBCF4926358"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_30C2EBCCC9EA55BB02305F38EED591BE"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_6FAACBE933ABC71A9EA2C0B07CE907A8"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_DC8004AC135904E56085322307851BAF"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3F9B34F45C576C98D45173B181DF633A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A58358AD0913F984E89FB196A683E490"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_97A992293FF9B9A879C10D28B64E7D37"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_C9E6868BAB58440582321B5162CB30D5"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_02515BD70C02D89E680D427047428AE1"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_425A36504A6828631091637046658E47"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9FD447983DD7E1575C65C6DAEF121524"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_96E7546D71956346FC6EEBBB1A432B77"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4EB37E11C4E392E17AF881E14CE5E58C"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4FFB44359A3B0E987E9322181DA789C4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_50494896C6B7942A0981F05C0B3D17D6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1A4FBFF8BF375A542B8E19BD244A13A7"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_8B5638B87DF0DF5232B1D506C7AD8AA6"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1AA4DA6CB720DF1811DF7A89E507891A"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_2BE2B7FFEEB27167FD54D5235F985673"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CD02533B06AC19E1A80E92E25ED26BDD"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_ED8EBD605C990D7017A6C579302F74E3"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_1B55927CF5A2205C039658DEDB0DA5A2"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_4C853350C7789E9D84C60FE3240C11F0"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_CCDA3E1E6E5B8588A367274ABCC1A335"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_3A65B2F8A62C663E667E2015527BD46E"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_45764A7EBDBE7F8937DC4A2D49365ED4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_A4FD7FF240232C28833686D6605BE802"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_52EECE1ED22627FD22E268EA1BEB9FA4"
        "MsmSig" = "8:_UNDEFINED"
        }
        "Entry"
        {
        "MsmKey" = "8:_UNDEFINED"
        "OwnerKey" = "8:_9E53ADBDA5349368D72FFF36E6370D6B"
        "MsmSig" = "8:_UNDEFINED"
        }
    }
    "Configurations"
    {
        "Debug"
        {
        "DisplayName" = "8:Debug"
        "IsDebugOnly" = "11:TRUE"
        "IsReleaseOnly" = "11:FALSE"
        "OutputFilename" = "8:Debug\\JOY.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
        }
        "Release"
        {
        "DisplayName" = "8:Release"
        "IsDebugOnly" = "11:FALSE"
        "IsReleaseOnly" = "11:TRUE"
        "OutputFilename" = "8:Release\\JOY.msi"
        "PackageFilesAs" = "3:2"
        "PackageFileSize" = "3:-2147483648"
        "CabType" = "3:1"
        "Compression" = "3:2"
        "SignOutput" = "11:FALSE"
        "CertificateFile" = "8:"
        "PrivateKeyFile" = "8:"
        "TimeStampServer" = "8:"
        "InstallerBootstrapper" = "3:2"
        }
    }
    "Deployable"
    {
        "CustomAction"
        {
        }
        "DefaultFeature"
        {
        "Name" = "8:DefaultFeature"
        "Title" = "8:"
        "Description" = "8:"
        }
        "ExternalPersistence"
        {
            "LaunchCondition"
            {
                "{A06ECF26-33A3-4562-8140-9B0E340D4F24}:_5B57BB28313C42BFB36622A1B9ED1BB0"
                {
                "Name" = "8:.NET Framework"
                "Message" = "8:[VSDNETMSG]"
                "FrameworkVersion" = "8:.NETFramework,Version=v4.7.2"
                "AllowLaterVersions" = "11:FALSE"
                "InstallUrl" = "8:http://go.microsoft.com/fwlink/?LinkId=863262"
                }
            }
        }
        "File"
        {
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_01E7DA0405145C166BD196B36BE68411"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_01E7DA0405145C166BD196B36BE68411"
                    {
                    "Name" = "8:System.Runtime.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_02515BD70C02D89E680D427047428AE1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Process, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_02515BD70C02D89E680D427047428AE1"
                    {
                    "Name" = "8:System.Diagnostics.Process.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Process.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0521DFDB0BA2B97655D6B6C1E1EA9877"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Cryptography.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_0521DFDB0BA2B97655D6B6C1E1EA9877"
                    {
                    "Name" = "8:System.Security.Cryptography.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Cryptography.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_05AF8E7CCC983CDAA1F6F276F952F030"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Costura, Version=4.1.0.0, Culture=neutral, PublicKeyToken=9919ef960d84173d, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_05AF8E7CCC983CDAA1F6F276F952F030"
                    {
                    "Name" = "8:Costura.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Costura.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_05D90BD716E44CB76FB1713306D5BE97"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:SharpDX.DXGI, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b4dcf0f35e5521f1, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_05D90BD716E44CB76FB1713306D5BE97"
                    {
                    "Name" = "8:SharpDX.DXGI.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:SharpDX.DXGI.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0C0A13A67B2C675C56DA2F113D493643"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection.Emit.ILGeneration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_0C0A13A67B2C675C56DA2F113D493643"
                    {
                    "Name" = "8:System.Reflection.Emit.ILGeneration.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.Emit.ILGeneration.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_0F6CC24C1F351E70F21634A04933D6C2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.XPath.XDocument, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_0F6CC24C1F351E70F21634A04933D6C2"
                    {
                    "Name" = "8:System.Xml.XPath.XDocument.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.XPath.XDocument.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_121F92DCBE07B0058402E414A64189CF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:SharpDX, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b4dcf0f35e5521f1, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_121F92DCBE07B0058402E414A64189CF"
                    {
                    "Name" = "8:SharpDX.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:SharpDX.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_122A3969849EACDE8C01155653D949DF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Cryptography.Encoding, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_122A3969849EACDE8C01155653D949DF"
                    {
                    "Name" = "8:System.Security.Cryptography.Encoding.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Cryptography.Encoding.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1410BF615C34D3C9C84E015AD0BBCEF6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.WebSockets.Client, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_1410BF615C34D3C9C84E015AD0BBCEF6"
                    {
                    "Name" = "8:System.Net.WebSockets.Client.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.WebSockets.Client.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1437729AD3038E139B3142CC334B92E6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Xml.ReaderWriter, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_1437729AD3038E139B3142CC334B92E6"
                    {
                    "Name" = "8:System.Xml.ReaderWriter.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.ReaderWriter.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_163864923D170DBAD2B11A6B9ACB67E9"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Serialization.Primitives, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_163864923D170DBAD2B11A6B9ACB67E9"
                    {
                    "Name" = "8:System.Runtime.Serialization.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Serialization.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1A4FBFF8BF375A542B8E19BD244A13A7"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ComponentModel.Primitives, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_1A4FBFF8BF375A542B8E19BD244A13A7"
                    {
                    "Name" = "8:System.ComponentModel.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ComponentModel.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1A5ED45D84425BF88AEE2C8B1EFBF670"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_1A5ED45D84425BF88AEE2C8B1EFBF670"
                    {
                    "Name" = "8:System.IO.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1AA4DA6CB720DF1811DF7A89E507891A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ComponentModel, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_1AA4DA6CB720DF1811DF7A89E507891A"
                    {
                    "Name" = "8:System.ComponentModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ComponentModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1AB2BFE8140D2D490B1A70BD653B65CB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Globalization.Extensions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_1AB2BFE8140D2D490B1A70BD653B65CB"
                    {
                    "Name" = "8:System.Globalization.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Globalization.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1B55927CF5A2205C039658DEDB0DA5A2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Collections, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_1B55927CF5A2205C039658DEDB0DA5A2"
                    {
                    "Name" = "8:System.Collections.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Collections.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_1DCC264D90984A9ECA1BB96446BB2056"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Linq.Parallel, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_1DCC264D90984A9ECA1BB96446BB2056"
                    {
                    "Name" = "8:System.Linq.Parallel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Linq.Parallel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_244221982303B8007636F85CB3DE2D32"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_244221982303B8007636F85CB3DE2D32"
                    {
                    "Name" = "8:System.Reflection.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_290AE77B93B0C97156B0DC6DFB563A58"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Cryptography.Algorithms, Version=4.3.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_290AE77B93B0C97156B0DC6DFB563A58"
                    {
                    "Name" = "8:System.Security.Cryptography.Algorithms.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Cryptography.Algorithms.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2B36C6015A9EE5DBEE4B780D3E97F079"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Serialization.Formatters, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2B36C6015A9EE5DBEE4B780D3E97F079"
                    {
                    "Name" = "8:System.Runtime.Serialization.Formatters.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Serialization.Formatters.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2BE2B7FFEEB27167FD54D5235F985673"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ComponentModel.Annotations, Version=4.0.10.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2BE2B7FFEEB27167FD54D5235F985673"
                    {
                    "Name" = "8:System.ComponentModel.Annotations.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ComponentModel.Annotations.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2E2EAA1F22B6745155FC98C537BB7EF7"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Cryptography.X509Certificates, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2E2EAA1F22B6745155FC98C537BB7EF7"
                    {
                    "Name" = "8:System.Security.Cryptography.X509Certificates.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Cryptography.X509Certificates.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2F60CE4A474335E4F755F83BC7AF5463"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.InteropServices.RuntimeInformation, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2F60CE4A474335E4F755F83BC7AF5463"
                    {
                    "Name" = "8:System.Runtime.InteropServices.RuntimeInformation.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.InteropServices.RuntimeInformation.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_2FF4CBC7171157C671DEB982FB2590D1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection.Emit, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_2FF4CBC7171157C671DEB982FB2590D1"
                    {
                    "Name" = "8:System.Reflection.Emit.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.Emit.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_30C2EBCCC9EA55BB02305F38EED591BE"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Globalization.Calendars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_30C2EBCCC9EA55BB02305F38EED591BE"
                    {
                    "Name" = "8:System.Globalization.Calendars.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Globalization.Calendars.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3260895E63550ABD91CF963566F43136"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.SecureString, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_3260895E63550ABD91CF963566F43136"
                    {
                    "Name" = "8:System.Security.SecureString.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.SecureString.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_35C8E32BE3BFF001B6D257E0924F7B19"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Resources.ResourceManager, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_35C8E32BE3BFF001B6D257E0924F7B19"
                    {
                    "Name" = "8:System.Resources.ResourceManager.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Resources.ResourceManager.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_370892C097F3F73C183C2B55006541D6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_370892C097F3F73C183C2B55006541D6"
                    {
                    "Name" = "8:System.Net.Http.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3A2A4C7095359B564895CAA15FC3C83D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Handles, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_3A2A4C7095359B564895CAA15FC3C83D"
                    {
                    "Name" = "8:System.Runtime.Handles.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Handles.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3A65B2F8A62C663E667E2015527BD46E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:netstandard, Version=2.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51"
                "ScatterAssemblies"
                {
                    "_3A65B2F8A62C663E667E2015527BD46E"
                    {
                    "Name" = "8:netstandard.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:netstandard.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3B8AB27C741625E1CE54376599F6BA1D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Resources.Reader, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_3B8AB27C741625E1CE54376599F6BA1D"
                    {
                    "Name" = "8:System.Resources.Reader.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Resources.Reader.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3DAC407B0CF2BA259D3578D5A2D0028D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Microsoft.Win32.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_3DAC407B0CF2BA259D3578D5A2D0028D"
                    {
                    "Name" = "8:Microsoft.Win32.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Microsoft.Win32.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_3F9B34F45C576C98D45173B181DF633A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.TraceSource, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_3F9B34F45C576C98D45173B181DF633A"
                    {
                    "Name" = "8:System.Diagnostics.TraceSource.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.TraceSource.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_425A36504A6828631091637046658E47"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.FileVersionInfo, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_425A36504A6828631091637046658E47"
                    {
                    "Name" = "8:System.Diagnostics.FileVersionInfo.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.FileVersionInfo.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4294E6EEC788C3DEE1EF1EE90D1EEA24"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Principal, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4294E6EEC788C3DEE1EF1EE90D1EEA24"
                    {
                    "Name" = "8:System.Security.Principal.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Principal.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_43D872F268806F29B3A0033DA7C24C09"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Threading, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_43D872F268806F29B3A0033DA7C24C09"
                    {
                    "Name" = "8:System.Threading.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4467D86EC33DC598842BC897FDF65EA7"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:SharpDX.Direct2D1, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b4dcf0f35e5521f1, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4467D86EC33DC598842BC897FDF65EA7"
                    {
                    "Name" = "8:SharpDX.Direct2D1.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:SharpDX.Direct2D1.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_45764A7EBDBE7F8937DC4A2D49365ED4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_45764A7EBDBE7F8937DC4A2D49365ED4"
                    {
                    "Name" = "8:System.Net.Http.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_49D5A849F7D8A9DB2B1EB306F4F21CBB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.ThreadPool, Version=4.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_49D5A849F7D8A9DB2B1EB306F4F21CBB"
                    {
                    "Name" = "8:System.Threading.ThreadPool.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.ThreadPool.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4C853350C7789E9D84C60FE3240C11F0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Collections.Concurrent, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4C853350C7789E9D84C60FE3240C11F0"
                    {
                    "Name" = "8:System.Collections.Concurrent.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Collections.Concurrent.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4D63B24A68881014A0EAA42598C21218"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Xamarin.Forms.Core, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_4D63B24A68881014A0EAA42598C21218"
                    {
                    "Name" = "8:Xamarin.Forms.Core.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Xamarin.Forms.Core.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4E024D6259FF671DCA757CDCEB6A6099"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.NameResolution, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4E024D6259FF671DCA757CDCEB6A6099"
                    {
                    "Name" = "8:System.Net.NameResolution.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.NameResolution.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4EB37E11C4E392E17AF881E14CE5E58C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Data.Common, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4EB37E11C4E392E17AF881E14CE5E58C"
                    {
                    "Name" = "8:System.Data.Common.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Data.Common.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4FA228E1D7CD6EDD504F3F39A54A3AA9"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ServiceModel.Duplex, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4FA228E1D7CD6EDD504F3F39A54A3AA9"
                    {
                    "Name" = "8:System.ServiceModel.Duplex.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ServiceModel.Duplex.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_4FFB44359A3B0E987E9322181DA789C4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Console, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_4FFB44359A3B0E987E9322181DA789C4"
                    {
                    "Name" = "8:System.Console.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Console.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_50494896C6B7942A0981F05C0B3D17D6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ComponentModel.TypeConverter, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_50494896C6B7942A0981F05C0B3D17D6"
                    {
                    "Name" = "8:System.ComponentModel.TypeConverter.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ComponentModel.TypeConverter.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_50C1C497D2DFE25F004301639713C44E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Serialization.Json, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_50C1C497D2DFE25F004301639713C44E"
                    {
                    "Name" = "8:System.Runtime.Serialization.Json.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Serialization.Json.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_52B22BAB453ECF56A867209CB96F7CED"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http.WebRequest, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_52B22BAB453ECF56A867209CB96F7CED"
                    {
                    "Name" = "8:System.Net.Http.WebRequest.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.WebRequest.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_52EECE1ED22627FD22E268EA1BEB9FA4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_52EECE1ED22627FD22E268EA1BEB9FA4"
                    {
                    "Name" = "8:System.IO.Compression.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Compression.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_578B38C765A941B123BE852D48340E99"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Claims, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_578B38C765A941B123BE852D48340E99"
                    {
                    "Name" = "8:System.Security.Claims.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Claims.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5861429DB5455D194632214DB099EF42"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Sockets, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_5861429DB5455D194632214DB099EF42"
                    {
                    "Name" = "8:System.Net.Sockets.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Sockets.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_587B09A3D3110602EB54F83438554349"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ServiceModel.Http, Version=4.0.10.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_587B09A3D3110602EB54F83438554349"
                    {
                    "Name" = "8:System.ServiceModel.Http.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ServiceModel.Http.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5A376FCFD60F88E1269AF207B9D17D8A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_5A376FCFD60F88E1269AF207B9D17D8A"
                    {
                    "Name" = "8:System.IO.FileSystem.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.FileSystem.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_5DF23DB30055BB5D0492B8431A8AD498"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Linq, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_5DF23DB30055BB5D0492B8431A8AD498"
                    {
                    "Name" = "8:System.Linq.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Linq.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_637742C64A0AD86931D4008FF5856DDC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_637742C64A0AD86931D4008FF5856DDC"
                    {
                    "Name" = "8:Newtonsoft.Json.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Newtonsoft.Json.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6429C4C71FC33109A9EDD4A5700D82AD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_6429C4C71FC33109A9EDD4A5700D82AD"
                    {
                    "Name" = "8:System.Runtime.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_671CDE26862A178FB690A9A1C7A89FFF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Requests, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_671CDE26862A178FB690A9A1C7A89FFF"
                    {
                    "Name" = "8:System.Net.Requests.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Requests.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_67B2B98EA3CFE87AF519D1294837B6EB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_67B2B98EA3CFE87AF519D1294837B6EB"
                    {
                    "Name" = "8:System.Threading.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6ADE07DC1D7A44FA68B838614F057967"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.IsolatedStorage, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_6ADE07DC1D7A44FA68B838614F057967"
                    {
                    "Name" = "8:System.IO.IsolatedStorage.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.IsolatedStorage.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6D4D9AE79192537D1C62F37DB44A3327"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_6D4D9AE79192537D1C62F37DB44A3327"
                    {
                    "Name" = "8:System.Reflection.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6E0D22B3835C10988ABDA9696E78288D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51"
                "ScatterAssemblies"
                {
                    "_6E0D22B3835C10988ABDA9696E78288D"
                    {
                    "Name" = "8:System.ValueTuple.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ValueTuple.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6FAACBE933ABC71A9EA2C0B07CE907A8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Dynamic.Runtime, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_6FAACBE933ABC71A9EA2C0B07CE907A8"
                    {
                    "Name" = "8:System.Dynamic.Runtime.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Dynamic.Runtime.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_6FEC36CE23840B7AE04E2A082B53981C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:SharpDX.Mathematics, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b4dcf0f35e5521f1, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_6FEC36CE23840B7AE04E2A082B53981C"
                    {
                    "Name" = "8:SharpDX.Mathematics.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:SharpDX.Mathematics.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7076989A598D73D988CB56CB2BBFE590"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.XDocument, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_7076989A598D73D988CB56CB2BBFE590"
                    {
                    "Name" = "8:System.Xml.XDocument.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.XDocument.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_71B44FB7BB0F87798964DC94B89EB61B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:FireSharp.Serialization.JsonNet, Version=1.1.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_71B44FB7BB0F87798964DC94B89EB61B"
                    {
                    "Name" = "8:FireSharp.Serialization.JsonNet.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:FireSharp.Serialization.JsonNet.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7542DEB7D5DC191B5FCB5BA77B34170F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.FileSystem.DriveInfo, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_7542DEB7D5DC191B5FCB5BA77B34170F"
                    {
                    "Name" = "8:System.IO.FileSystem.DriveInfo.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.FileSystem.DriveInfo.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7747EE074B5F3B1EBE70D9954F24C469"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.WebHeaderCollection, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_7747EE074B5F3B1EBE70D9954F24C469"
                    {
                    "Name" = "8:System.Net.WebHeaderCollection.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.WebHeaderCollection.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7775201DA141306EA9DCCA685D5360EB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.ComponentModel, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_7775201DA141306EA9DCCA685D5360EB"
                    {
                    "Name" = "8:System.ComponentModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ComponentModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_785D75DD8EB353EEBB0280AC6F2D8F2E"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Linq, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_785D75DD8EB353EEBB0280AC6F2D8F2E"
                    {
                    "Name" = "8:System.Linq.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Linq.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7939658D154EA0604AEF6D9426796236"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection.Emit.Lightweight, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_7939658D154EA0604AEF6D9426796236"
                    {
                    "Name" = "8:System.Reflection.Emit.Lightweight.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.Emit.Lightweight.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_794464E7967E653311BF715D99169EA3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Extensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_794464E7967E653311BF715D99169EA3"
                    {
                    "Name" = "8:System.Runtime.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_7D5AED969856061FB3DBB9885091DD30"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Xamarin.Forms.Platform, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_7D5AED969856061FB3DBB9885091DD30"
                    {
                    "Name" = "8:Xamarin.Forms.Platform.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Xamarin.Forms.Platform.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_833DD21D2AA70449BA759D9249A2971A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Text.Encoding, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_833DD21D2AA70449BA759D9249A2971A"
                    {
                    "Name" = "8:System.Text.Encoding.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Text.Encoding.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_89C9DA0672EB9718A517098B4DA23E7F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.XPath, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_89C9DA0672EB9718A517098B4DA23E7F"
                    {
                    "Name" = "8:System.Xml.XPath.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.XPath.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8A6250FE037075A5D5A1BD01EDF55E6C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.CompilerServices.VisualC, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_8A6250FE037075A5D5A1BD01EDF55E6C"
                    {
                    "Name" = "8:System.Runtime.CompilerServices.VisualC.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.CompilerServices.VisualC.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8B5638B87DF0DF5232B1D506C7AD8AA6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ComponentModel.EventBasedAsync, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_8B5638B87DF0DF5232B1D506C7AD8AA6"
                    {
                    "Name" = "8:System.ComponentModel.EventBasedAsync.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ComponentModel.EventBasedAsync.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_8F5E03BC65509F9E3E398AEED92114EA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.WebSockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_8F5E03BC65509F9E3E398AEED92114EA"
                    {
                    "Name" = "8:System.Net.WebSockets.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.WebSockets.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_915381240883D9E2AAE24999DDEEBD57"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.InteropServices, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_915381240883D9E2AAE24999DDEEBD57"
                    {
                    "Name" = "8:System.Runtime.InteropServices.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.InteropServices.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_954D407CAAD57516740BDE7350CC72ED"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ServiceModel.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_954D407CAAD57516740BDE7350CC72ED"
                    {
                    "Name" = "8:System.ServiceModel.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ServiceModel.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_96ACBD054FE73116E89D8CFC37BBEFB8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.ReaderWriter, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_96ACBD054FE73116E89D8CFC37BBEFB8"
                    {
                    "Name" = "8:System.Xml.ReaderWriter.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.ReaderWriter.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_96E7546D71956346FC6EEBBB1A432B77"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Contracts, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_96E7546D71956346FC6EEBBB1A432B77"
                    {
                    "Name" = "8:System.Diagnostics.Contracts.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Contracts.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_97A992293FF9B9A879C10D28B64E7D37"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.TextWriterTraceListener, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_97A992293FF9B9A879C10D28B64E7D37"
                    {
                    "Name" = "8:System.Diagnostics.TextWriterTraceListener.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.TextWriterTraceListener.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9A2E06F3E0B7683F2A6CFD87BBDE3848"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.InteropServices.WindowsRuntime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_9A2E06F3E0B7683F2A6CFD87BBDE3848"
                    {
                    "Name" = "8:System.Runtime.InteropServices.WindowsRuntime.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.InteropServices.WindowsRuntime.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9D75182B096F27AFC396BE6BC397D158"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CheckBoxRadio, Version=5.5.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_9D75182B096F27AFC396BE6BC397D158"
                    {
                    "Name" = "8:CheckBoxRadio.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CheckBoxRadio.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9E53ADBDA5349368D72FFF36E6370D6B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Tracing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_9E53ADBDA5349368D72FFF36E6370D6B"
                    {
                    "Name" = "8:System.Diagnostics.Tracing.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Tracing.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9FD447983DD7E1575C65C6DAEF121524"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Debug, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_9FD447983DD7E1575C65C6DAEF121524"
                    {
                    "Name" = "8:System.Diagnostics.Debug.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Debug.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_9FF64FD7B43E528DFF1AE401583E91B4"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.XmlSerializer, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_9FF64FD7B43E528DFF1AE401583E91B4"
                    {
                    "Name" = "8:System.Xml.XmlSerializer.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.XmlSerializer.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A0FA8D2CBAE77F6B4C5A3231623B2771"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Text.RegularExpressions, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_A0FA8D2CBAE77F6B4C5A3231623B2771"
                    {
                    "Name" = "8:System.Text.RegularExpressions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Text.RegularExpressions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A2982F13E183DA52C5CBB8D1BB7C1A47"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Primitives, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_A2982F13E183DA52C5CBB8D1BB7C1A47"
                    {
                    "Name" = "8:System.Net.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A484E235D28E37FE0708AD6CD36353A9"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Linq.Expressions, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_A484E235D28E37FE0708AD6CD36353A9"
                    {
                    "Name" = "8:System.Linq.Expressions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Linq.Expressions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A4FD7FF240232C28833686D6605BE802"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.IO.Compression.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_A4FD7FF240232C28833686D6605BE802"
                    {
                    "Name" = "8:System.IO.Compression.FileSystem.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Compression.FileSystem.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A58358AD0913F984E89FB196A683E490"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Tools, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_A58358AD0913F984E89FB196A683E490"
                    {
                    "Name" = "8:System.Diagnostics.Tools.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Tools.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A5877197275074769BFEE5E40B034142"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Security, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_A5877197275074769BFEE5E40B034142"
                    {
                    "Name" = "8:System.Net.Security.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Security.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_A6FC4E907839CCAFF20593CD9A6D2F02"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Ping, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_A6FC4E907839CCAFF20593CD9A6D2F02"
                    {
                    "Name" = "8:System.Net.Ping.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Ping.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_AB54C2D4993F5793B384E1CFD3C6D6EE"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Reflection, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_AB54C2D4993F5793B384E1CFD3C6D6EE"
                    {
                    "Name" = "8:System.Reflection.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_ACEF7F113BF7FD71C9B3E57FA01B814C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Resources.Writer, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_ACEF7F113BF7FD71C9B3E57FA01B814C"
                    {
                    "Name" = "8:System.Resources.Writer.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Resources.Writer.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_AEFA980BC329863B0614B407F66C52B0"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ObjectModel, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_AEFA980BC329863B0614B407F66C52B0"
                    {
                    "Name" = "8:System.ObjectModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ObjectModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_AFBC7E7BF4C18126774926259B0E3425"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Resources.ResourceManager, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_AFBC7E7BF4C18126774926259B0E3425"
                    {
                    "Name" = "8:System.Resources.ResourceManager.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Resources.ResourceManager.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_AFF7581BD9D784C95295D67D33FE7186"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.Pipes, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_AFF7581BD9D784C95295D67D33FE7186"
                    {
                    "Name" = "8:System.IO.Pipes.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Pipes.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B0B7A91FB07B2E6033BBA473C033D3C8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Reflection.Extensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_B0B7A91FB07B2E6033BBA473C033D3C8"
                    {
                    "Name" = "8:System.Reflection.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B0DAEB9E903BCF31818C3958A191FF3A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Linq.Expressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_B0DAEB9E903BCF31818C3958A191FF3A"
                    {
                    "Name" = "8:System.Linq.Expressions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Linq.Expressions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B1DEF0F5B8E69CB5F096ADD785002E07"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_B1DEF0F5B8E69CB5F096ADD785002E07"
                    {
                    "Name" = "8:System.IO.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B9CC798D2A0250AF834483C1B92B3F9C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Serialization.Xml, Version=4.1.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_B9CC798D2A0250AF834483C1B92B3F9C"
                    {
                    "Name" = "8:System.Runtime.Serialization.Xml.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Serialization.Xml.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_B9ED22CAC73D9163773CB1D65DD6ACA1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Tasks, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_B9ED22CAC73D9163773CB1D65DD6ACA1"
                    {
                    "Name" = "8:System.Threading.Tasks.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Tasks.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BA710DA6F15000524F4F869E8A16372B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:Xamarin.Forms.Xaml, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_BA710DA6F15000524F4F869E8A16372B"
                    {
                    "Name" = "8:Xamarin.Forms.Xaml.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:Xamarin.Forms.Xaml.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_BA947C97364E3C01A5D68AC444C75ED8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
                "ScatterAssemblies"
                {
                    "_BA947C97364E3C01A5D68AC444C75ED8"
                    {
                    "Name" = "8:System.IO.Compression.ZipFile.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.Compression.ZipFile.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C06D7742C83385E6850DBB2C4C924C11"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Linq.Queryable, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_C06D7742C83385E6850DBB2C4C924C11"
                    {
                    "Name" = "8:System.Linq.Queryable.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Linq.Queryable.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C177140B344CED9E0E47B3701347555B"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.ObjectModel, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C177140B344CED9E0E47B3701347555B"
                    {
                    "Name" = "8:System.ObjectModel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ObjectModel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C2DE2FBBA9DAB06C0577DBBCF4926358"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Globalization, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_C2DE2FBBA9DAB06C0577DBBCF4926358"
                    {
                    "Name" = "8:System.Globalization.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Globalization.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C4D556C2B8910F9AC4BEF606843C84C6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Text.RegularExpressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C4D556C2B8910F9AC4BEF606843C84C6"
                    {
                    "Name" = "8:System.Text.RegularExpressions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Text.RegularExpressions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C54B2F804682DF339389A3C2DD40377C"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.Debug, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_C54B2F804682DF339389A3C2DD40377C"
                    {
                    "Name" = "8:System.Diagnostics.Debug.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.Debug.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C59EFC5B6729D91776B3F0289D939AF1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_C59EFC5B6729D91776B3F0289D939AF1"
                    {
                    "Name" = "8:System.IO.FileSystem.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.FileSystem.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_C9E6868BAB58440582321B5162CB30D5"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Diagnostics.StackTrace, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_C9E6868BAB58440582321B5162CB30D5"
                    {
                    "Name" = "8:System.Diagnostics.StackTrace.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Diagnostics.StackTrace.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CB6D921CCD2BDFBE468FACCD9602C357"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.FileSystem.Watcher, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_CB6D921CCD2BDFBE468FACCD9602C357"
                    {
                    "Name" = "8:System.IO.FileSystem.Watcher.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.FileSystem.Watcher.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CB7887B831473B396A59E6115D2B843D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Extensions, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_CB7887B831473B396A59E6115D2B843D"
                    {
                    "Name" = "8:System.Runtime.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CCDA3E1E6E5B8588A367274ABCC1A335"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.AppContext, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_CCDA3E1E6E5B8588A367274ABCC1A335"
                    {
                    "Name" = "8:System.AppContext.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.AppContext.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CD02533B06AC19E1A80E92E25ED26BDD"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Collections.Specialized, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_CD02533B06AC19E1A80E92E25ED26BDD"
                    {
                    "Name" = "8:System.Collections.Specialized.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Collections.Specialized.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CE3CC3B21A8CE50D6E5FB0B18345E00A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Globalization, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_CE3CC3B21A8CE50D6E5FB0B18345E00A"
                    {
                    "Name" = "8:System.Globalization.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Globalization.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_CFF3E59DA41AD34E2FBA4CF407FFAFE6"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Text.Encoding.Extensions, Version=4.0.11.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_CFF3E59DA41AD34E2FBA4CF407FFAFE6"
                    {
                    "Name" = "8:System.Text.Encoding.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Text.Encoding.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D338FE4406FAE5E496A913A834B18EDA"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.MemoryMappedFiles, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_D338FE4406FAE5E496A913A834B18EDA"
                    {
                    "Name" = "8:System.IO.MemoryMappedFiles.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.MemoryMappedFiles.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_D8C401EBD38AD3DE4582E00A8E0BFB0F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Thread, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_D8C401EBD38AD3DE4582E00A8E0BFB0F"
                    {
                    "Name" = "8:System.Threading.Thread.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Thread.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DB916CBDF914C2BF2D82CC7F1F1EA563"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Dynamic.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_DB916CBDF914C2BF2D82CC7F1F1EA563"
                    {
                    "Name" = "8:System.Dynamic.Runtime.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Dynamic.Runtime.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DC8004AC135904E56085322307851BAF"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Drawing.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_DC8004AC135904E56085322307851BAF"
                    {
                    "Name" = "8:System.Drawing.Primitives.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Drawing.Primitives.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_DF87D965968AEE951B9DF14653E13A74"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.IO.UnmanagedMemoryStream, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_DF87D965968AEE951B9DF14653E13A74"
                    {
                    "Name" = "8:System.IO.UnmanagedMemoryStream.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.IO.UnmanagedMemoryStream.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E3CD88AB6F8A3B28B9395A678E2BA886"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ServiceModel.NetTcp, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_E3CD88AB6F8A3B28B9395A678E2BA886"
                    {
                    "Name" = "8:System.ServiceModel.NetTcp.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ServiceModel.NetTcp.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E4D00C202B558BE30BF6A291D6CCF8B2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Tasks.Parallel, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_E4D00C202B558BE30BF6A291D6CCF8B2"
                    {
                    "Name" = "8:System.Threading.Tasks.Parallel.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Tasks.Parallel.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E5966CDA4988BA92BBF9127214BB9F93"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:RestSharp, Version=104.4.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_E5966CDA4988BA92BBF9127214BB9F93"
                    {
                    "Name" = "8:RestSharp.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:RestSharp.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_E673A41F0DA9F34CFA3993064821637A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Security.Cryptography.Csp, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_E673A41F0DA9F34CFA3993064821637A"
                    {
                    "Name" = "8:System.Security.Cryptography.Csp.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Security.Cryptography.Csp.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_EA2F99B8DDB3A91714F583AF2F0BE2E2"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.NetworkInformation, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_EA2F99B8DDB3A91714F583AF2F0BE2E2"
                    {
                    "Name" = "8:System.Net.NetworkInformation.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.NetworkInformation.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_ED8EBD605C990D7017A6C579302F74E3"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Collections.NonGeneric, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_ED8EBD605C990D7017A6C579302F74E3"
                    {
                    "Name" = "8:System.Collections.NonGeneric.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Collections.NonGeneric.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_EDAE5995396C436BFA1194D0AEF66CEC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Xml.XmlDocument, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_EDAE5995396C436BFA1194D0AEF66CEC"
                    {
                    "Name" = "8:System.Xml.XmlDocument.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Xml.XmlDocument.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_EF160C805523A42E0BBBE5838DB4A6A8"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Timer, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_EF160C805523A42E0BBBE5838DB4A6A8"
                    {
                    "Name" = "8:System.Threading.Timer.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Timer.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F0B9B5CEB7A5CF3438F97A69894CF59F"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Net.Http.Rtc, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_F0B9B5CEB7A5CF3438F97A69894CF59F"
                    {
                    "Name" = "8:System.Net.Http.Rtc.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Net.Http.Rtc.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F1F4ADCEA929833BC0E2BD77985D98DC"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Reflection.Extensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_F1F4ADCEA929833BC0E2BD77985D98DC"
                    {
                    "Name" = "8:System.Reflection.Extensions.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Reflection.Extensions.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F36B12514A33C936C3AB887A26E02945"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Overlapped, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_F36B12514A33C936C3AB887A26E02945"
                    {
                    "Name" = "8:System.Threading.Overlapped.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Overlapped.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F687DEFA2759F19EE0CA0C773C9A28E1"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:FireSharp, Version=1.1.0.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_F687DEFA2759F19EE0CA0C773C9A28E1"
                    {
                    "Name" = "8:FireSharp.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:FireSharp.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_F83D575C3E81B8B958D7A64028A20D94"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.ServiceModel.Security, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_F83D575C3E81B8B958D7A64028A20D94"
                    {
                    "Name" = "8:System.ServiceModel.Security.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.ServiceModel.Security.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FC7343B4C7B2E730AB64B00B5C59689A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Collections, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FC7343B4C7B2E730AB64B00B5C59689A"
                    {
                    "Name" = "8:System.Collections.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Collections.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FED81A06B70597B23453141A39311922"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:TRUE"
            "AssemblyAsmDisplayName" = "8:System.Threading.Tasks, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FED81A06B70597B23453141A39311922"
                    {
                    "Name" = "8:System.Threading.Tasks.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Threading.Tasks.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FEE158D67CE54365E0858AA479E4AF4A"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:GameOverlay, Version=*******, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FEE158D67CE54365E0858AA479E4AF4A"
                    {
                    "Name" = "8:GameOverlay.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:GameOverlay.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FF6C3968935A714E6E4F741DCD3A60AB"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:CheckButton, Version=1.0.2.0, Culture=neutral, processorArchitecture=MSIL"
                "ScatterAssemblies"
                {
                    "_FF6C3968935A714E6E4F741DCD3A60AB"
                    {
                    "Name" = "8:CheckButton.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:CheckButton.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
            "{9F6F8455-1EF1-4B85-886A-4223BCC8E7F7}:_FFA0D44FC402BE0AE2511B33F1F90A1D"
            {
            "AssemblyRegister" = "3:1"
            "AssemblyIsInGAC" = "11:FALSE"
            "AssemblyAsmDisplayName" = "8:System.Runtime.Numerics, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
                "ScatterAssemblies"
                {
                    "_FFA0D44FC402BE0AE2511B33F1F90A1D"
                    {
                    "Name" = "8:System.Runtime.Numerics.dll"
                    "Attributes" = "3:512"
                    }
                }
            "SourcePath" = "8:System.Runtime.Numerics.dll"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:TRUE"
            "IsolateTo" = "8:"
            }
        }
        "FileType"
        {
        }
        "Folder"
        {
            "{1525181F-901A-416C-8A58-119130FE478E}:_1A9A7C1B124C4A44B9699038D1937BD0"
            {
            "Name" = "8:#1916"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:DesktopFolder"
                "Folders"
                {
                }
            }
            "{3C67513D-01DD-4637-8A68-80971EB9504F}:_4FA1B0DDBD524F7C84A116D2005642F6"
            {
            "DefaultLocation" = "8:[ProgramFilesFolder][Manufacturer]\\[ProductName]"
            "Name" = "8:#1925"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:TARGETDIR"
                "Folders"
                {
                }
            }
            "{1525181F-901A-416C-8A58-119130FE478E}:_DD31A5224E34450AA609137A26AF2FBB"
            {
            "Name" = "8:#1919"
            "AlwaysCreate" = "11:FALSE"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Property" = "8:ProgramMenuFolder"
                "Folders"
                {
                }
            }
        }
        "LaunchCondition"
        {
        }
        "Locator"
        {
        }
        "MsiBootstrapper"
        {
        "LangId" = "3:1033"
        "RequiresElevation" = "11:FALSE"
        }
        "Product"
        {
        "Name" = "8:Microsoft Visual Studio"
        "ProductName" = "8:JOY"
        "ProductCode" = "8:{DDE3B381-7DD1-43A6-AF29-3A8C875958DB}"
        "PackageCode" = "8:{E0764166-848B-4AF4-A33B-4C139B3DB5A0}"
        "UpgradeCode" = "8:{A223D956-5D3B-4A86-A80A-D7D2D49BCE98}"
        "AspNetVersion" = "8:4.0.30319.0"
        "RestartWWWService" = "11:FALSE"
        "RemovePreviousVersions" = "11:FALSE"
        "DetectNewerInstalledVersion" = "11:TRUE"
        "InstallAllUsers" = "11:FALSE"
        "ProductVersion" = "8:1.0.0"
        "Manufacturer" = "8:Default Company Name"
        "ARPHELPTELEPHONE" = "8:"
        "ARPHELPLINK" = "8:"
        "Title" = "8:JOY"
        "Subject" = "8:"
        "ARPCONTACT" = "8:Default Company Name"
        "Keywords" = "8:"
        "ARPCOMMENTS" = "8:"
        "ARPURLINFOABOUT" = "8:"
        "ARPPRODUCTICON" = "8:"
        "ARPIconIndex" = "3:0"
        "SearchPath" = "8:"
        "UseSystemSearchPath" = "11:TRUE"
        "TargetPlatform" = "3:0"
        "PreBuildEvent" = "8:"
        "PostBuildEvent" = "8:"
        "RunPostBuildEvent" = "3:0"
        }
        "Registry"
        {
            "HKLM"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_80CA4C3920B44B0A9D5E66D2BC0A5524"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_52378F7557CE4A2B8B843B80310C3394"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCU"
            {
                "Keys"
                {
                    "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_B261211B293F4983B33C54E456E651C7"
                    {
                    "Name" = "8:Software"
                    "Condition" = "8:"
                    "AlwaysCreate" = "11:FALSE"
                    "DeleteAtUninstall" = "11:FALSE"
                    "Transitive" = "11:FALSE"
                        "Keys"
                        {
                            "{60EA8692-D2D5-43EB-80DC-7906BF13D6EF}:_F356CC6E3AA447D69ECD840F3677831E"
                            {
                            "Name" = "8:[Manufacturer]"
                            "Condition" = "8:"
                            "AlwaysCreate" = "11:FALSE"
                            "DeleteAtUninstall" = "11:FALSE"
                            "Transitive" = "11:FALSE"
                                "Keys"
                                {
                                }
                                "Values"
                                {
                                }
                            }
                        }
                        "Values"
                        {
                        }
                    }
                }
            }
            "HKCR"
            {
                "Keys"
                {
                }
            }
            "HKU"
            {
                "Keys"
                {
                }
            }
            "HKPU"
            {
                "Keys"
                {
                }
            }
        }
        "Sequences"
        {
        }
        "Shortcut"
        {
        }
        "UserInterface"
        {
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_3C24EDB58C3B4458A2FB1AEF394C918A"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:2"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_21DB8D9F4CC447E89082183C24C0ED09"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_2A0475C28A2446EDB80132F1BAC23D0B"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_35760794F9B04DF18A73489ADC425061"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_9C41E6F5D0764471A8E381E42EDB1E7D"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdBasicDialogs.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_9D3022F4B1E848DA8BBEB8F01DC0D8DB"
            {
            "Name" = "8:#1900"
            "Sequence" = "3:1"
            "Attributes" = "3:1"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_3BB26D1E9AF54BCC9805951E0288A911"
                    {
                    "Sequence" = "3:200"
                    "DisplayName" = "8:Installation Folder"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFolderDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "InstallAllUsersVisible"
                            {
                            "Name" = "8:InstallAllUsersVisible"
                            "DisplayName" = "8:#1059"
                            "Description" = "8:#1159"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_3E5A9263DCAA42E5B1EFAA84490830AA"
                    {
                    "Sequence" = "3:300"
                    "DisplayName" = "8:Confirm Installation"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdConfirmDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_5682B0714D444D60A4EDE43931DCFCB0"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Welcome"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdWelcomeDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "CopyrightWarning"
                            {
                            "Name" = "8:CopyrightWarning"
                            "DisplayName" = "8:#1002"
                            "Description" = "8:#1102"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1202"
                            "DefaultValue" = "8:#1202"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "Welcome"
                            {
                            "Name" = "8:Welcome"
                            "DisplayName" = "8:#1003"
                            "Description" = "8:#1103"
                            "Type" = "3:3"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1203"
                            "DefaultValue" = "8:#1203"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_AB91275044B741A4AF3490F134E51829"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:2"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_65AEDBBF109A4A1E95980CF9AA172B72"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_AE568CC4591E46D3817B52E94E179C45"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:2"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_B62D4304C4EC46A18C81A882CF89AA05"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdAdminFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_BD953493B8934D67A5D20CF4D8924F94"
            {
            "Name" = "8:#1901"
            "Sequence" = "3:1"
            "Attributes" = "3:2"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_BD59AEF3BB654EE29C499A3CE20AE7C0"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Progress"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdProgressDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "ShowProgress"
                            {
                            "Name" = "8:ShowProgress"
                            "DisplayName" = "8:#1009"
                            "Description" = "8:#1109"
                            "Type" = "3:5"
                            "ContextData" = "8:1;True=1;False=0"
                            "Attributes" = "3:0"
                            "Setting" = "3:0"
                            "Value" = "3:1"
                            "DefaultValue" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
            "{2479F3F5-0309-486D-8047-8187E2CE5BA0}:_C9C9075B53E54F03B133302AD344EA47"
            {
            "UseDynamicProperties" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "SourcePath" = "8:<VsdDialogDir>\\VsdUserInterface.wim"
            }
            "{DF760B10-853B-4699-99F2-AFF7185B4A62}:_EFCB5BA6C3E3418BB03A1F6906CF71F2"
            {
            "Name" = "8:#1902"
            "Sequence" = "3:1"
            "Attributes" = "3:3"
                "Dialogs"
                {
                    "{688940B3-5CA9-4162-8DEE-2993FA9D8CBC}:_611F6504E6FF4CDDA823D7E696FFF14D"
                    {
                    "Sequence" = "3:100"
                    "DisplayName" = "8:Finished"
                    "UseDynamicProperties" = "11:TRUE"
                    "IsDependency" = "11:FALSE"
                    "SourcePath" = "8:<VsdDialogDir>\\VsdFinishedDlg.wid"
                        "Properties"
                        {
                            "BannerBitmap"
                            {
                            "Name" = "8:BannerBitmap"
                            "DisplayName" = "8:#1001"
                            "Description" = "8:#1101"
                            "Type" = "3:8"
                            "ContextData" = "8:Bitmap"
                            "Attributes" = "3:4"
                            "Setting" = "3:1"
                            "UsePlugInResources" = "11:TRUE"
                            }
                            "UpdateText"
                            {
                            "Name" = "8:UpdateText"
                            "DisplayName" = "8:#1058"
                            "Description" = "8:#1158"
                            "Type" = "3:15"
                            "ContextData" = "8:"
                            "Attributes" = "3:0"
                            "Setting" = "3:1"
                            "Value" = "8:#1258"
                            "DefaultValue" = "8:#1258"
                            "UsePlugInResources" = "11:TRUE"
                            }
                        }
                    }
                }
            }
        }
        "MergeModule"
        {
        }
        "ProjectOutput"
        {
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_1FD56EFEE24C41A9B3A25C615BC4C559"
            {
            "SourcePath" = "8:"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:RuntimeImplementation"
            "OutputProjectGuid" = "8:{69B42FC8-AB08-415D-A225-7F0FE305EAEB}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_4480DD1F33A647C08E77D512FD247552"
            {
            "SourcePath" = "8:"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:ReferenceCopyLocalPaths"
            "OutputProjectGuid" = "8:{69B42FC8-AB08-415D-A225-7F0FE305EAEB}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_44F8CFDDBA3841E1B39703B35FF380B6"
            {
            "SourcePath" = "8:obj\\Debug\\JOY.exe"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:Built"
            "OutputProjectGuid" = "8:{69B42FC8-AB08-415D-A225-7F0FE305EAEB}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_51490C192558475FAE770DD1BDC2AF71"
            {
            "SourcePath" = "8:"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:Documentation"
            "OutputProjectGuid" = "8:{69B42FC8-AB08-415D-A225-7F0FE305EAEB}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_53BBC18467144E50BAED866733BC36D4"
            {
            "SourcePath" = "8:"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:ContentFiles"
            "OutputProjectGuid" = "8:{69B42FC8-AB08-415D-A225-7F0FE305EAEB}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_55F46F70688043AF92865F2F7273185E"
            {
            "SourcePath" = "8:"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:Symbols"
            "OutputProjectGuid" = "8:{69B42FC8-AB08-415D-A225-7F0FE305EAEB}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_5C185C79461848188B6CE28184E409A9"
            {
            "SourcePath" = "8:"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:XmlSerializer"
            "OutputProjectGuid" = "8:{69B42FC8-AB08-415D-A225-7F0FE305EAEB}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_76D143112F9D489BA8911EAF61923EBB"
            {
            "SourcePath" = "8:"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:LocalizedResourceDlls"
            "OutputProjectGuid" = "8:{69B42FC8-AB08-415D-A225-7F0FE305EAEB}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
            "{5259A561-127C-4D43-A0A1-72F10C7B3BF8}:_FDF53D4EA98B409C9F11C99A2972A677"
            {
            "SourcePath" = "8:"
            "TargetName" = "8:"
            "Tag" = "8:"
            "Folder" = "8:_4FA1B0DDBD524F7C84A116D2005642F6"
            "Condition" = "8:"
            "Transitive" = "11:FALSE"
            "Vital" = "11:TRUE"
            "ReadOnly" = "11:FALSE"
            "Hidden" = "11:FALSE"
            "System" = "11:FALSE"
            "Permanent" = "11:FALSE"
            "SharedLegacy" = "11:FALSE"
            "PackageAs" = "3:1"
            "Register" = "3:1"
            "Exclude" = "11:FALSE"
            "IsDependency" = "11:FALSE"
            "IsolateTo" = "8:"
            "ProjectOutputGroupRegister" = "3:1"
            "OutputConfiguration" = "8:"
            "OutputGroupCanonicalName" = "8:SourceFiles"
            "OutputProjectGuid" = "8:{69B42FC8-AB08-415D-A225-7F0FE305EAEB}"
            "ShowKeyOutput" = "11:TRUE"
                "ExcludeFilters"
                {
                }
            }
        }
    }
}
