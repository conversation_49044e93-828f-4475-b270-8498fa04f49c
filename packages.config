﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="CheckBoxRadio" version="5.5.0" targetFramework="net472" />
  <package id="CheckButton" version="1.0.2" targetFramework="net472" />
  <package id="Costura.Fody" version="4.1.0" targetFramework="net472" />
  <package id="FireSharp" version="1.1.0" targetFramework="net472" />
  <package id="FireSharp.Serialization.JsonNet" version="1.1.0" targetFramework="net472" />
  <package id="Fody" version="6.0.0" targetFramework="net461" developmentDependency="true" />
  <package id="GameOverlay.Net" version="4.3.1" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="7.0.0" targetFramework="net472" />
  <package id="Microsoft.CodeAnalysis.VersionCheckAnalyzer" version="3.3.0" targetFramework="net472" developmentDependency="true" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net472" />
  <package id="RestSharp" version="110.2.0" targetFramework="net472" />
  <package id="SharpDX" version="4.2.0" targetFramework="net472" />
  <package id="SharpDX.Direct2D1" version="4.2.0" targetFramework="net472" />
  <package id="SharpDX.DXGI" version="4.2.0" targetFramework="net472" />
  <package id="SharpDX.Mathematics" version="4.2.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="7.0.0" targetFramework="net472" />
  <package id="System.Text.Json" version="7.0.2" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
  <package id="Xamarin.Forms" version="2.4.0.280" targetFramework="net472" />
</packages>