﻿<?xml version="1.0" encoding="utf-8"?>
<Roots xmlns="clr-namespace:Microsoft.Xaml.Tools.XamlCompiler.RootLog;assembly=Microsoft.Windows.UI.Xaml.81.Build.Tasks">
  <Roots.Interfaces>
    <RootInterface FullName="Windows.UI.Xaml.Data.IValueConverter" />
  </Roots.Interfaces>
  <Roots.PropertyPathNames>
    <RootPropertyPathName Name="Cell" />
    <RootPropertyPathName Name="RenderHeight" />
    <RootPropertyPathName Name="View" />
    <RootPropertyPathName Name="Title" />
    <RootPropertyPathName Name="Text" />
    <RootPropertyPathName Name="TemplateSettings" />
    <RootPropertyPathName Name="IndicatorLengthDelta" />
    <RootPropertyPathName Name="ElementOpacity" />
    <RootPropertyPathName Name="EllipseAnimationEndPosition" />
    <RootPropertyPathName Name="EllipseAnimationWellPosition" />
    <RootPropertyPathName Name="ContainerAnimationStartPosition" />
    <RootPropertyPathName Name="ContainerAnimationEndPosition" />
    <RootPropertyPathName Name="EllipseDiameter" />
    <RootPropertyPathName Name="EllipseOffset" />
    <RootPropertyPathName Name="TextAlignment" />
    <RootPropertyPathName Name="Background" />
    <RootPropertyPathName Name="CurtainOffToOnOffset" />
    <RootPropertyPathName Name="KnobOffToOnOffset" />
    <RootPropertyPathName Name="CurtainOnToOffOffset" />
    <RootPropertyPathName Name="KnobOnToOffOffset" />
    <RootPropertyPathName Name="CurtainCurrentToOffOffset" />
    <RootPropertyPathName Name="KnobCurrentToOffOffset" />
    <RootPropertyPathName Name="CurtainCurrentToOnOffset" />
    <RootPropertyPathName Name="KnobCurrentToOnOffset" />
    <RootPropertyPathName Name="Foreground" />
    <RootPropertyPathName Name="BorderBrush" />
    <RootPropertyPathName Name="FontWeight" />
    <RootPropertyPathName Name="FontSize" />
    <RootPropertyPathName Name="FontFamily" />
    <RootPropertyPathName Name="TitleInset" />
    <RootPropertyPathName Name="Icon" />
    <RootPropertyPathName Name="Value" />
    <RootPropertyPathName Name="BarBackgroundColor" />
    <RootPropertyPathName Name="IsEnabled" />
    <RootPropertyPathName Name="Placeholder" />
    <RootPropertyPathName Name="HorizontalTextAlignment" />
    <RootPropertyPathName Name="Keyboard" />
    <RootPropertyPathName Name="Label" />
    <RootPropertyPathName Name="LabelColor" />
    <RootPropertyPathName Name="On" />
    <RootPropertyPathName Name="Detail" />
    <RootPropertyPathName Name="DetailColor" />
    <RootPropertyPathName Name="TextColor" />
    <RootPropertyPathName Name="ImageSource" />
  </Roots.PropertyPathNames>
  <Roots.RootTypes>
    <RootType FullName="Windows.UI.Xaml.ResourceDictionary">
      <RootProperty Name="MergedDictionaries" />
      <RootProperty Name="Source" />
    </RootType>
    <RootType FullName="Xamarin.Forms.Platform.WinRT.CaseConverter">
      <RootProperty Name="ConvertToUpper" />
    </RootType>
    <RootType FullName="Xamarin.Forms.Platform.WinRT.HeightConverter" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.CollapseWhenEmptyConverter" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.BoolToVisibilityConverter" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.PageToRenderedElementConverter" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.ImageConverter" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.ViewToRendererConverter" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.ColorConverter" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.HorizontalTextAlignmentConverter" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.KeyboardConverter" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.MasterBackgroundConverter" />
    <RootType FullName="Windows.UI.Xaml.Controls.Canvas" />
    <RootType FullName="Windows.UI.Xaml.Controls.Panel">
      <RootProperty Name="Background" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ListView">
      <RootProperty Name="Background" />
      <RootProperty Name="IsTabStop" />
      <RootProperty Name="IsItemClickEnabled" />
      <RootProperty Name="Resources" />
      <RootProperty Name="ItemTemplate" />
      <RootProperty Name="ItemContainerTransitions" />
      <RootProperty Name="ItemContainerStyle" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ListViewBase">
      <RootProperty Name="SelectionMode" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ItemsControl">
      <RootProperty Name="ItemContainerStyle" />
      <RootProperty Name="ItemTemplate" />
      <RootProperty Name="ItemsPanel" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.DataTemplate">
      <RootProperty Name="Template" />
    </RootType>
    <RootType FullName="Xamarin.Forms.Platform.WinRT.ListViewGroupStyleSelector" />
    <RootType FullName="Windows.UI.Xaml.Controls.TextBox">
      <RootProperty Name="SelectionHighlightColor" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.FrameworkElement">
      <RootProperty Name="Margin" />
      <RootProperty Name="MinHeight" />
      <RootProperty Name="VerticalAlignment" />
      <RootProperty Name="MinWidth" />
      <RootProperty Name="HorizontalAlignment" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Shapes.Path">
      <RootProperty Name="Data" />
      <RootProperty Name="Fill" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Setter">
      <RootProperty Name="Property" />
      <RootProperty Name="Value" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ContentPresenter">
      <RootProperty Name="Content" />
      <RootProperty Name="Resources" />
      <RootProperty Name="Height" />
      <RootProperty Name="Foreground" />
      <RootProperty Name="Margin" />
      <RootProperty Name="ContentTemplate" />
      <RootProperty Name="FontWeight" />
      <RootProperty Name="HorizontalAlignment" />
      <RootProperty Name="IsHitTestVisible" />
      <RootProperty Name="MinWidth" />
      <RootProperty Name="Opacity" />
      <RootProperty Name="VerticalAlignment" />
      <RootProperty Name="ContentTransitions" />
      <RootProperty Name="FontSize" />
      <RootProperty Name="FontFamily" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Data.Binding">
      <RootProperty Name="Converter" />
      <RootProperty Name="RelativeSource" />
      <RootProperty Name="Path" />
      <RootProperty Name="ElementName" />
      <RootProperty Name="ConverterParameter" />
      <RootProperty Name="Mode" />
      <RootProperty Name="UpdateSourceTrigger" />
    </RootType>
    <RootType FullName="Microsoft.Xaml.Tools.DirectUI.ProxyTypes.StaticResourceExtension">
      <RootProperty Name="ResourceKey" />
    </RootType>
    <RootType FullName="Xamarin.Forms.Platform.WinRT.PageControl">
      <RootProperty Name="ContentMargin" />
      <RootProperty Name="TitleBrush" />
      <RootProperty Name="InvisibleBackButtonCollapsed" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.Control">
      <RootProperty Name="Background" />
      <RootProperty Name="HorizontalContentAlignment" />
      <RootProperty Name="Foreground" />
      <RootProperty Name="BorderBrush" />
      <RootProperty Name="BorderThickness" />
      <RootProperty Name="IsTabStop" />
      <RootProperty Name="Template" />
      <RootProperty Name="FontFamily" />
      <RootProperty Name="FontSize" />
      <RootProperty Name="Padding" />
      <RootProperty Name="VerticalContentAlignment" />
      <RootProperty Name="FontWeight" />
      <RootProperty Name="TabNavigation" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ContentControl">
      <RootProperty Name="DataContext" />
      <RootProperty Name="Content" />
      <RootProperty Name="Foreground" />
      <RootProperty Name="Margin" />
      <RootProperty Name="Padding" />
      <RootProperty Name="IsTabStop" />
      <RootProperty Name="IsHitTestVisible" />
      <RootProperty Name="HorizontalAlignment" />
      <RootProperty Name="Visibility" />
      <RootProperty Name="VerticalAlignment" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.TextBlock">
      <RootProperty Name="Margin" />
      <RootProperty Name="Text" />
      <RootProperty Name="Style" />
      <RootProperty Name="Visibility" />
      <RootProperty Name="HorizontalAlignment" />
      <RootProperty Name="VerticalAlignment" />
      <RootProperty Name="Foreground" />
      <RootProperty Name="FontStyle" />
      <RootProperty Name="FontFamily" />
      <RootProperty Name="TextTrimming" />
      <RootProperty Name="Name" />
      <RootProperty Name="Padding" />
      <RootProperty Name="FontWeight" />
      <RootProperty Name="Opacity" />
    </RootType>
    <RootType FullName="Microsoft.Xaml.Tools.DirectUI.ProxyTypes.ThemeResourceExtension" />
    <RootType FullName="Windows.UI.Xaml.Data.RelativeSource">
      <RootProperty Name="Mode" />
    </RootType>
    <RootType FullName="Xamarin.Forms.Platform.WinRT.CellControl">
      <RootProperty Name="HorizontalContentAlignment" />
      <RootProperty Name="Height" />
      <RootProperty Name="IsGroupHeader" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ListViewItem" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.FormsProgressBar" />
    <RootType FullName="Windows.UI.Xaml.Controls.Primitives.RangeBase">
      <RootProperty Name="Maximum" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ControlTemplate">
      <RootProperty Name="TargetType" />
      <RootProperty Name="Template" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.Grid">
      <RootProperty Name="Children" />
      <RootProperty Name="Opacity" />
      <RootProperty Name="RenderTransform" />
      <RootProperty Name="ColumnDefinitions" />
      <RootProperty Name="Resources" />
      <RootProperty Name="RowDefinitions" />
      <RootProperty Name="Margin" />
      <RootProperty Name="Background" />
      <RootProperty Name="ManipulationMode" />
      <RootProperty Name="Height" />
      <RootProperty Name="MaxHeight" />
      <RootProperty Name="Visibility" />
      <RootProperty Name="VerticalAlignment" />
      <RootProperty Name="HorizontalAlignment" />
      <RootMethod Name="GetColumn" />
      <RootMethod Name="SetColumn" />
      <RootMethod Name="GetRow" />
      <RootMethod Name="SetRow" />
      <RootMethod Name="GetColumnSpan" />
      <RootMethod Name="SetColumnSpan" />
      <RootMethod Name="GetRowSpan" />
      <RootMethod Name="SetRowSpan" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.VisualStateManager">
      <RootMethod Name="GetVisualStateGroups" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.VisualStateGroup">
      <RootProperty Name="Transitions" />
      <RootProperty Name="States" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.VisualTransition">
      <RootProperty Name="From" />
      <RootProperty Name="To" />
      <RootProperty Name="Storyboard" />
      <RootProperty Name="GeneratedDuration" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.Storyboard">
      <RootProperty Name="Children" />
      <RootProperty Name="RepeatBehavior" />
      <RootMethod Name="GetTargetName" />
      <RootMethod Name="SetTargetName" />
      <RootMethod Name="GetTargetProperty" />
      <RootMethod Name="SetTargetProperty" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.FadeInThemeAnimation">
      <RootProperty Name="TargetName" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.DoubleAnimation">
      <RootProperty Name="To" />
      <RootProperty Name="Duration" />
      <RootProperty Name="From" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.RepositionThemeAnimation">
      <RootProperty Name="TargetName" />
      <RootProperty Name="FromHorizontalOffset" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.VisualState">
      <RootProperty Name="Storyboard" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.ObjectAnimationUsingKeyFrames">
      <RootProperty Name="KeyFrames" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.DiscreteObjectKeyFrame">
      <RootProperty Name="KeyTime" />
      <RootProperty Name="Value" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.DoubleAnimationUsingKeyFrames">
      <RootProperty Name="KeyFrames" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.FadeOutThemeAnimation">
      <RootProperty Name="TargetName" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.EasingDoubleKeyFrame">
      <RootProperty Name="KeyTime" />
      <RootProperty Name="Value" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.SplineDoubleKeyFrame">
      <RootProperty Name="KeyTime" />
      <RootProperty Name="Value" />
      <RootProperty Name="KeySpline" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.Border">
      <RootProperty Name="Background" />
      <RootProperty Name="BorderBrush" />
      <RootProperty Name="BorderThickness" />
      <RootProperty Name="Child" />
      <RootProperty Name="RenderTransformOrigin" />
      <RootProperty Name="RenderTransform" />
      <RootProperty Name="Margin" />
      <RootProperty Name="MinWidth" />
      <RootProperty Name="Resources" />
      <RootProperty Name="Visibility" />
      <RootProperty Name="VerticalAlignment" />
      <RootProperty Name="Padding" />
      <RootProperty Name="MinHeight" />
    </RootType>
    <RootType FullName="Microsoft.Xaml.Tools.DirectUI.ProxyTypes.TemplateBindingExtension" />
    <RootType FullName="Windows.UI.Xaml.Shapes.Rectangle">
      <RootProperty Name="Margin" />
      <RootProperty Name="Fill" />
      <RootProperty Name="HorizontalAlignment" />
      <RootProperty Name="Width" />
      <RootProperty Name="Stroke" />
      <RootProperty Name="StrokeThickness" />
      <RootProperty Name="RenderTransform" />
      <RootProperty Name="Opacity" />
      <RootProperty Name="StrokeDashOffset" />
      <RootProperty Name="StrokeEndLineCap" />
      <RootProperty Name="StrokeDashArray" />
      <RootProperty Name="IsHitTestVisible" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.TranslateTransform">
      <RootProperty Name="X" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ColumnDefinition">
      <RootProperty Name="Width" />
      <RootProperty Name="MinWidth" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Shapes.Ellipse">
      <RootProperty Name="Fill" />
      <RootProperty Name="Width" />
      <RootProperty Name="Height" />
      <RootProperty Name="RenderTransformOrigin" />
      <RootProperty Name="RenderTransform" />
    </RootType>
    <RootType FullName="Xamarin.Forms.Platform.WinRT.TextAlignmentToHorizontalAlignmentConverter" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.FormsTextBox">
      <RootProperty Name="PlaceholderForegroundBrush" />
      <RootProperty Name="TextAlignment" />
      <RootProperty Name="BorderThickness" />
      <RootProperty Name="Background" />
      <RootProperty Name="Foreground" />
      <RootProperty Name="FontWeight" />
      <RootProperty Name="FontSize" />
      <RootProperty Name="FontFamily" />
      <RootProperty Name="InputScope" />
      <RootProperty Name="MinHeight" />
      <RootProperty Name="MaxLength" />
      <RootProperty Name="Padding" />
      <RootProperty Name="PlaceholderText" />
      <RootProperty Name="Style" />
      <RootProperty Name="TextWrapping" />
      <RootProperty Name="VerticalAlignment" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ScrollViewer">
      <RootProperty Name="Background" />
      <RootProperty Name="HorizontalScrollMode" />
      <RootProperty Name="HorizontalScrollBarVisibility" />
      <RootProperty Name="VerticalScrollMode" />
      <RootProperty Name="VerticalScrollBarVisibility" />
      <RootProperty Name="IsHorizontalRailEnabled" />
      <RootProperty Name="IsVerticalRailEnabled" />
      <RootProperty Name="IsDeferredScrollingEnabled" />
      <RootProperty Name="Margin" />
      <RootProperty Name="Padding" />
      <RootProperty Name="IsTabStop" />
      <RootProperty Name="ZoomMode" />
      <RootProperty Name="Content" />
      <RootMethod Name="GetHorizontalScrollBarVisibility" />
      <RootMethod Name="SetHorizontalScrollBarVisibility" />
      <RootMethod Name="GetVerticalScrollBarVisibility" />
      <RootMethod Name="SetVerticalScrollBarVisibility" />
      <RootMethod Name="GetIsDeferredScrollingEnabled" />
      <RootMethod Name="SetIsDeferredScrollingEnabled" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.Button">
      <RootProperty Name="Style" />
      <RootProperty Name="BorderThickness" />
      <RootProperty Name="IsTabStop" />
      <RootProperty Name="Visibility" />
      <RootProperty Name="FontSize" />
      <RootProperty Name="VerticalAlignment" />
      <RootProperty Name="Background" />
      <RootProperty Name="FontWeight" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Automation.AutomationProperties">
      <RootMethod Name="GetAccessibilityView" />
      <RootMethod Name="SetAccessibilityView" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Visibility" />
    <RootType FullName="Windows.UI.Xaml.Controls.RowDefinition">
      <RootProperty Name="Height" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.SolidColorBrush">
      <RootProperty Name="Color" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.GridView" />
    <RootType FullName="Windows.UI.Xaml.Controls.GroupStyle">
      <RootProperty Name="HidesIfEmpty" />
      <RootProperty Name="HeaderContainerStyle" />
      <RootProperty Name="HeaderTemplate" />
    </RootType>
    <RootType FullName="Xamarin.Forms.Platform.WinRT.TabButton">
      <RootProperty Name="Content" />
    </RootType>
    <RootType FullName="Xamarin.Forms.Platform.WinRT.TabsControl" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.FormsSearchBox" />
    <RootType FullName="Windows.UI.Xaml.Documents.Typography">
      <RootMethod Name="GetStylisticSet20" />
      <RootMethod Name="SetStylisticSet20" />
      <RootMethod Name="GetDiscretionaryLigatures" />
      <RootMethod Name="SetDiscretionaryLigatures" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ToggleSwitch">
      <RootProperty Name="IsOn" />
      <RootProperty Name="OnContent" />
      <RootProperty Name="OffContent" />
      <RootProperty Name="VerticalAlignment" />
    </RootType>
    <RootType FullName="System.Boolean" />
    <RootType FullName="Windows.UI.Xaml.Controls.Primitives.Thumb">
      <RootProperty Name="Template" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.Primitives.Popup">
      <RootProperty Name="HorizontalAlignment" />
      <RootProperty Name="VerticalAlignment" />
      <RootProperty Name="Child" />
      <RootProperty Name="IsLightDismissEnabled" />
      <RootProperty Name="ChildTransitions" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.PointerDownThemeAnimation">
      <RootProperty Name="TargetName" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.RichTextBlock">
      <RootProperty Name="SelectionHighlightColor" />
      <RootProperty Name="TextWrapping" />
      <RootProperty Name="TextTrimming" />
      <RootProperty Name="Resources" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Documents.Run">
      <RootProperty Name="Foreground" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.Image">
      <RootProperty Name="Height" />
      <RootProperty Name="Margin" />
      <RootProperty Name="Width" />
      <RootProperty Name="DataContext" />
      <RootProperty Name="Source" />
      <RootProperty Name="HorizontalAlignment" />
      <RootProperty Name="VerticalAlignment" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.TransitionCollection" />
    <RootType FullName="Windows.UI.Xaml.Controls.AppBarButton">
      <RootProperty Name="Margin" />
      <RootProperty Name="Foreground" />
      <RootProperty Name="HorizontalAlignment" />
      <RootProperty Name="VerticalAlignment" />
      <RootProperty Name="Visibility" />
      <RootProperty Name="Icon" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ToolTipService">
      <RootMethod Name="GetToolTip" />
      <RootMethod Name="SetToolTip" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.SymbolIcon">
      <RootProperty Name="Symbol" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.PaneThemeTransition">
      <RootProperty Name="Edge" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.StackPanel">
      <RootProperty Name="VerticalAlignment" />
      <RootProperty Name="Children" />
      <RootProperty Name="Name" />
      <RootProperty Name="Orientation" />
      <RootProperty Name="Margin" />
      <RootProperty Name="Background" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ItemsPanelTemplate">
      <RootProperty Name="Template" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ItemsPresenter" />
    <RootType FullName="Windows.UI.Xaml.Media.Animation.PointerUpThemeAnimation">
      <RootProperty Name="TargetName" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ListViewHeaderItem" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.ListGroupHeaderPresenter" />
    <RootType FullName="Xamarin.Forms.Platform.WinRT.EntryCellTextBox">
      <RootProperty Name="Margin" />
      <RootProperty Name="IsEnabled" />
      <RootProperty Name="Header" />
      <RootProperty Name="Text" />
      <RootProperty Name="PlaceholderText" />
      <RootProperty Name="TextAlignment" />
      <RootProperty Name="InputScope" />
      <RootProperty Name="HorizontalAlignment" />
      <RootProperty Name="HeaderTemplate" />
    </RootType>
    <RootType FullName="Xamarin.Forms.Platform.WinRT.FormsListViewItemPresenter">
      <RootProperty Name="CheckBrush" />
      <RootProperty Name="CheckHintBrush" />
      <RootProperty Name="CheckSelectingBrush" />
      <RootProperty Name="ContentMargin" />
      <RootProperty Name="DisabledOpacity" />
      <RootProperty Name="DragBackground" />
      <RootProperty Name="DragForeground" />
      <RootProperty Name="DragOpacity" />
      <RootProperty Name="FocusBorderBrush" />
      <RootProperty Name="PlaceholderBackground" />
      <RootProperty Name="PointerOverBackground" />
      <RootProperty Name="PointerOverBackgroundMargin" />
      <RootProperty Name="ReorderHintOffset" />
      <RootProperty Name="SelectedBackground" />
      <RootProperty Name="SelectedBorderThickness" />
      <RootProperty Name="SelectedForeground" />
      <RootProperty Name="SelectedPointerOverBackground" />
      <RootProperty Name="SelectedPointerOverBorderBrush" />
      <RootProperty Name="SelectionCheckMarkVisualEnabled" />
    </RootType>
  </Roots.RootTypes>
</Roots>
