[/Script/ShadowTrackerExtra.STExtraBaseCharacter]
ClientHitPartJudgment=MAX
UseShootVerifyEx=false

[/Script/ShadowTrackerExtra.STExtraGameInstance]
+SwitchesInMaps=(MapName="shooting_range4",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="PUBG_Forest",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="PUBG_Desert",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="PUBG_Savage_Main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="DihorOtok_Main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="TD_Logging_Camp_Main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="PVE_Infection_main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="HP_City_Main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="TD_Ruins_Half",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="TD_Factory_Depot_Mian",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="VehicleBattle_TDM_PUBG_Forest",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="SocialIsland_Main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="SocialIsland_Private_Main",Switches=((Key="t.MaxFPS",Value="60")))

+RenderStyleParamsInMaps=(DynamicStyleName=ERenderDynamicStyle::Default, PostProcessParams=("ColorSaturation|(X=2.300000,Y=2.300000,Z=2.300000,W=2.300000)"), ACESParams=(TintColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),Bright=0.500000,Gray=1.00000,ShoulderStrength=2.330000,ToeStrength=0.140000,LinearStrength=0.500000,LinearAngle=0.060000))
+MobileMSAAOpenConfig=(RenderKey="r.ACESStyle", RenderValue=5)
+MobileMSAAOpenConfig=(RenderKey="t.MaxFPS", RenderValue=60)
+MobileMSAACloseConfig=(RenderKey="r.ACESStyle", RenderValue=5)
+MobileMSAACloseConfig=(RenderKey="t.MaxFPS", RenderValue=60)

[/Script/ShadowTrackerExtra.STExtraPlayerController]
+SwitchesInMaps=(MapName="shooting_range4",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="PUBG_Forest",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="PUBG_Desert",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="PUBG_Savage_Main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="DihorOtok_Main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="TD_Logging_Camp_Main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="PVE_Infection_main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="HP_City_Main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="TD_Ruins_Half",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="TD_Factory_Depot_Mian",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="VehicleBattle_TDM_PUBG_Forest",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="SocialIsland_Main",Switches=((Key="t.MaxFPS",Value="60")))
+SwitchesInMaps=(MapName="SocialIsland_Private_Main",Switches=((Key="t.MaxFPS",Value="60")))

[/Script/ShadowTrackerExtra.ShootWeaponEntity]
AnimationKick=0.03f
AccessoriesRecoveryFactor=3.0f
AccessoriesHRecoilFactor=3.0f
AccessoriesVRecoilFactor=3.0f
AccessoriesDeviationFactor=3.0f