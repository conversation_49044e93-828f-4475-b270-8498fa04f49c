<doc>
    <assembly>
        <name>Xamarin.Forms.Xaml</name>
    </assembly>
    <members>
        <member name="T:Xamarin.Forms.Xaml.ArrayExtension">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.ArrayExtension">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.ArrayExtension.Items">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.ArrayExtension.ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.ArrayExtension.ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.ArrayExtension.ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.ArrayExtension.Type">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.ArrayExtension.Xamarin#Forms#Xaml#IMarkupExtension#ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.BindingExtension">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.BindingExtension">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.BindingExtension.Converter">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.BindingExtension.ConverterParameter">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.BindingExtension.Mode">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.BindingExtension.Path">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.BindingExtension.Source">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.BindingExtension.StringFormat">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.BindingExtension.TypedBinding">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.BindingExtension.UpdateSourceEventName">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.BindingExtension.Xamarin#Forms#Xaml#IMarkupExtension#ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.BindingExtension.Xamarin#Forms#Xaml#IMarkupExtension{Xamarin#Forms#BindingBase}#ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.DynamicResourceExtension">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.DynamicResourceExtension">
            <summary>Internal.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.DynamicResourceExtension.Key">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.DynamicResourceExtension.ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.DynamicResourceExtension.Xamarin#Forms#Xaml#IMarkupExtension{Xamarin#Forms#DynamicResource}#ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.DynamicResourceExtension.Xamarin#Forms#Xaml#IMarkupExtension{Xamarin#Forms#Internals#DynamicResource}#ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.Extensions">
            <summary>Extension class for <see cref="T:Xamarin.Forms.View" /> defining <see cref="M:Xamarin.Forms.Xaml.Extensions.LoadFromXaml{TView}" /> method.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.Extensions.LoadFromXaml``1(``0,System.Type)">
            <typeparam name="TXaml">To be added.</typeparam>
            <param name="view">For internal use by the XAML infrastructure.</param>
            <param name="callingType">For internal use by the XAML infrastructure.</param>
            <summary>Configures <paramref name="view" /> with the properties that are defined in the application manifest for <paramref name="callingType" />.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.Extensions.LoadFromXaml``1(``0,System.Type)">
            <typeparam name="TXaml">To be added.</typeparam>
            <param name="view">For internal use by the XAML infrastructure.</param>
            <param name="callingType">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.NullExtension">
            <summary>Extension class that differentiates between null values and empty strings.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.NullExtension">
            <summary>Creates a new <see cref="T:Xamarin.Forms.Xaml.NullExtension" /> object with default values.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.NullExtension.ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>Returns the null object.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.ReferenceExtension">
            <summary>Markup extension for referring to other XAML-defined types.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.ReferenceExtension">
            <summary>Creates a new <see cref="T:Xamarin.Forms.Xaml.ReferenceExtension" /> with default values.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.ReferenceExtension.Name">
            <summary>Gets or sets the name of the entity to reference.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.ReferenceExtension.ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>Returns an object that represents the type that was referred to.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.StaticExtension">
            <summary>A markup extension that gets a static member value.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.StaticExtension">
            <summary>Creates a new <see cref="T:Xamarin.Forms.Xaml.StaticExtension" /> object with default values.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.StaticExtension.Member">
            <summary>Gets or sets the member name.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.StaticExtension.ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>Returns the value of the member.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.StaticResourceExtension">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.StaticResourceExtension">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.StaticResourceExtension.Key">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.StaticResourceExtension.ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.TemplateBindingExtension">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.TemplateBindingExtension">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.TemplateBindingExtension.Converter">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.TemplateBindingExtension.ConverterParameter">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.TemplateBindingExtension.Mode">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.TemplateBindingExtension.Path">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.TemplateBindingExtension.StringFormat">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.TemplateBindingExtension.Xamarin#Forms#Xaml#IMarkupExtension#ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.TemplateBindingExtension.Xamarin#Forms#Xaml#IMarkupExtension{Xamarin#Forms#BindingBase}#ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.TypeExtension">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.TypeExtension">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.TypeExtension.ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.TypeExtension.ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.TypeExtension.TypeName">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.TypeExtension.Xamarin#Forms#Xaml#IMarkupExtension#ProvideValue(System.IServiceProvider)">
            <param name="serviceProvider">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.XamlCompilationAttribute">
            <summary>Attribute that controls whether XAML will be compiled at build time or run time.</summary>
            <remarks>By default, XAML compilation at build time is turned off.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.XamlCompilationAttribute(Xamarin.Forms.Xaml.XamlCompilationOptions)">
            <param name="xamlCompilationOptions">A value that tells whether to compile XAML at run time or compile time.</param>
            <summary>Creates a new <see cref="T:Xamarin.Forms.Xaml.XamlCompilationAttribute" /> with the specified value.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.XamlCompilationAttribute.XamlCompilationOptions">
            <summary>Gets or sets a value that tells whether to compile XAML at run time or compile time.</summary>
            <value>A value that tells whether to compile XAML at run time or compile time.</value>
            <remarks>By default, XAML compilation at build time is turned off.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.XamlCompilationOptions">
            <summary>Enumerates values that control when XAML is compiled into IL.</summary>
            <remarks>
                <para>Enabling build-time compilation by specifying the <c>Compile</c> option checks the XAML at build time, reduces loading time, and produces a smaller assembly or application.</para>
                <para>By default, XAML compilation at build time is turned off.</para>
            </remarks>
        </member>
        <member name="F:Xamarin.Forms.Xaml.XamlCompilationOptions.Compile">
            <summary>Compile the XAML for the class or project when the application is built.</summary>
        </member>
        <member name="F:Xamarin.Forms.Xaml.XamlCompilationOptions.Skip">
            <summary>Compile the XAML for the class or project when the application is run on the device.</summary>
        </member>
        <member name="T:Xamarin.Forms.Xaml.XamlFilePathAttribute">
            <summary>For internal use by the Xaml infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.XamlFilePathAttribute(System.String)">
            <param name="filePath">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the Xaml infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.XamlParseException">
            <summary>Exception that is raised when the XAML parser encounters a XAML error.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.XmlLineInfo">
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.XmlLineInfo(System.Boolean,System.Int32,System.Int32)">
            <param name="hasLineInfo">For internal use by the XAML infrastructure.</param>
            <param name="linenumber">For internal use by the XAML infrastructure.</param>
            <param name="lineposition">For internal use by the XAML infrastructure.</param>
            <summary>For internal use by the XAML infrastructure.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.XmlLineInfo.HasLineInfo">
            <summary>For internal use by the XAML infrastructure.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.XmlLineInfo.LineNumber">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.XmlLineInfo.LinePosition">
            <summary>For internal use by the XAML infrastructure.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.Internals.NameScopeProvider">
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.Internals.NameScopeProvider">
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.Internals.NameScopeProvider.NameScope">
            <summary>For internal use by the XAML platform.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.Internals.SimpleValueTargetProvider">
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.Internals.SimpleValueTargetProvider(System.Object[])">
            <param name="objectAndParents">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.Internals.SimpleValueTargetProvider(System.Object[],System.Object)">
            <param name="objectAndParents">For internal use by the Xamarin.Forms platform.</param>
            <param name="targetProperty">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.Internals.SimpleValueTargetProvider.Xamarin#Forms#Xaml#IProvideValueTarget#TargetObject">
            <summary>For internal use by the XAML platform.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.Internals.SimpleValueTargetProvider.Xamarin#Forms#Xaml#IProvideValueTarget#TargetProperty">
            <summary>For internal use by the XAML platform.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.Internals.XamlLoader">
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.Internals.XamlLoader.XamlFileProvider">
            <summary>For internal use by the XAML platform.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.Internals.XamlServiceProvider">
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.Internals.XamlServiceProvider">
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.Internals.XamlServiceProvider.Add(System.Type,System.Object)">
            <param name="type">For internal use by the Xamarin.Forms platform.</param>
            <param name="service">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.Internals.XamlServiceProvider.GetService(System.Type)">
            <param name="serviceType">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.Internals.XamlTypeResolver">
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.Internals.XamlTypeResolver(System.Xml.IXmlNamespaceResolver,System.Reflection.Assembly)">
            <param name="namespaceResolver">For internal use by the Xamarin.Forms platform.</param>
            <param name="currentAssembly">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.Internals.XamlTypeResolver.Xamarin#Forms#Xaml#IXamlTypeResolver#Resolve(System.String,System.IServiceProvider)">
            <param name="qualifiedTypeName">For internal use by the Xamarin.Forms platform.</param>
            <param name="serviceProvider">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.Internals.XamlTypeResolver.Xamarin#Forms#Xaml#IXamlTypeResolver#TryResolve(System.String,System.Type@)">
            <param name="qualifiedTypeName">For internal use by the Xamarin.Forms platform.</param>
            <param name="type">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.Internals.XmlLineInfoProvider">
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.Internals.XmlLineInfoProvider(System.Xml.IXmlLineInfo)">
            <param name="xmlLineInfo">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="P:Xamarin.Forms.Xaml.Internals.XmlLineInfoProvider.XmlLineInfo">
            <summary>For internal use by the XAML platform.</summary>
            <value>To be added.</value>
            <remarks>To be added.</remarks>
        </member>
        <member name="T:Xamarin.Forms.Xaml.Internals.XmlNamespaceResolver">
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="C:Xamarin.Forms.Xaml.Internals.XmlNamespaceResolver">
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.Internals.XmlNamespaceResolver.Add(System.String,System.String)">
            <param name="prefix">For internal use by the Xamarin.Forms platform.</param>
            <param name="ns">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.Internals.XmlNamespaceResolver.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
            <param name="scope">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.Internals.XmlNamespaceResolver.LookupNamespace(System.String)">
            <param name="prefix">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
        <member name="M:Xamarin.Forms.Xaml.Internals.XmlNamespaceResolver.LookupPrefix(System.String)">
            <param name="namespaceName">For internal use by the Xamarin.Forms platform.</param>
            <summary>For internal use by the XAML platform.</summary>
            <returns>To be added.</returns>
            <remarks>To be added.</remarks>
        </member>
    </members>
</doc>
