﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace JOY.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("JOY.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Byte[].
        /// </summary>
        internal static byte[] adb {
            get {
                object obj = ResourceManager.GetObject("adb", resourceCulture);
                return ((byte[])(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Byte[].
        /// </summary>
        internal static byte[] AdbWinApi {
            get {
                object obj = ResourceManager.GetObject("AdbWinApi", resourceCulture);
                return ((byte[])(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [FansSwitcher]
        ///+CVars=r.PUBGDeviceFPSLow=60
        ///
        ///[FansDeviceProfile]
        ///+CVars=r.DeviceType=IOS
        ///+CVars=r.BaseProfileName=MARGamerz
        ///
        ///[FansCustom]
        ///+CVars=0B572C0A1C0B280C1815100D002A1C0D0D10171E4449
        ///+CVars=0B572C0A1C0B2A11181D160E2A0E100D1A114448
        ///+CVars=0B572A11181D160E280C1815100D004449
        ///+CVars=0B5734161B10151C3A16170D1C170D2A1A18151C3F181A0D160B44485749
        ///+CVars=0B572C0A1C0B2F0C151218172A1C0D0D10171E4448
        ///+CVars=0B5734161B10151C313D2B44495749
        ///+CVars=0B5734161B10151C572A1A1C171C3A1615160B3F160B14180D44495749
        ///+CVars=0B5 [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string EnjoyCJZC {
            get {
                return ResourceManager.GetString("EnjoyCJZC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap space_5454443_1920 {
            get {
                object obj = ResourceManager.GetObject("space-5454443_1920", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [version]
        ///appversion=1.1.0.14460
        ///srcversion=1.1.0.14550.
        /// </summary>
        internal static string SrcVersion {
            get {
                return ResourceManager.GetString("SrcVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [ConsoleVariables]
        ///weapon.EnableBurstShootCD=0
        ///stweapon.ForbidHitDataReport=0
        ///stweapon.ForbidWeaponAntiComp=0
        ///AutoAimEnable=1
        ///AttrModifyComponentEnable=1
        ///BuffComponentEnable=1
        ///t.MaxFPS=60
        ///s.EnableCrashHandler=0
        ///s.IosUploadZipLog=0
        ///s.EnableUploadZipLog=0
        ///s.UploadCrashLog=0
        ///s.ShippingLogToDisk=0
        ///chara.CanEnableRagdoll=0
        ///r.UseProgramBinaryCache=0
        ///EnableWeaponAttackFlow=0
        ///EnableSecAttackFlow=0
        ///r.ACESStyle=5
        ///InGamePerformanceTracking.Enabled=0
        ///printanticheatlog=0
        ///
        ///[Pak]
        ///EnableBanLowVersion= [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string UserEngine {
            get {
                return ResourceManager.GetString("UserEngine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [/Script/ShadowTrackerExtra.STExtraBaseCharacter]
        ///ClientHitPartJudgment=MAX
        ///UseShootVerifyEx=false
        ///
        ///[/Script/ShadowTrackerExtra.STExtraGameInstance]
        ///+SwitchesInMaps=(MapName=&quot;shooting_range4&quot;,Switches=((Key=&quot;t.MaxFPS&quot;,Value=&quot;60&quot;)))
        ///+SwitchesInMaps=(MapName=&quot;PUBG_Forest&quot;,Switches=((Key=&quot;t.MaxFPS&quot;,Value=&quot;60&quot;)))
        ///+SwitchesInMaps=(MapName=&quot;PUBG_Desert&quot;,Switches=((Key=&quot;t.MaxFPS&quot;,Value=&quot;60&quot;)))
        ///+SwitchesInMaps=(MapName=&quot;PUBG_Savage_Main&quot;,Switches=((Key=&quot;t.MaxFPS&quot;,Value=&quot;60&quot;)))
        ///+SwitchesInMaps=(MapName=&quot;Dih [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string UserGame {
            get {
                return ResourceManager.GetString("UserGame", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [Core.Log]
        ///LogOnline=off
        ///LogOnlineGame=off
        ///LogHttp=off
        ///LogSTEOnlineGame=off
        ///LogCircle=off
        ///LogItemGeneratorBase=off
        ///LogBulletHitImpact=off
        ///LogGCloud=off
        ///LogClass=off
        ///LogSTCharMoveSpecial=off
        ///LogAntiCheat=off
        ///
        ///[ShippingCore.Log]
        ///LogInit=log
        ///LogTaskGraph=off
        ///LogDevObjectVersion=off
        ///LogMemory=off
        ///LogTextLocalizationManager=off
        ///LogObj=off
        ///LogExit=off
        ///LogPlatformFile=off
        ///LogOnline=off
        ///LogOnlineGame=off
        ///LogHttp=off
        ///LogSTEOnlineGame=off
        ///LogCircle=off
        ///LogItemGeneratorBase=off
        ///LogBulletHitImpact=off
        ///LogTemp=off
        ///LogSc [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string UserLogSuppression {
            get {
                return ResourceManager.GetString("UserLogSuppression", resourceCulture);
            }
        }
    }
}
