[Core.Log]
LogOnline=off
LogOnlineGame=off
LogHttp=off
LogSTEOnlineGame=off
LogCircle=off
LogItemGeneratorBase=off
LogBulletHitImpact=off
LogGCloud=off
LogClass=off
LogSTCharMoveSpecial=off
LogAntiCheat=off

[ShippingCore.Log]
LogInit=log
LogTaskGraph=off
LogDevObjectVersion=off
LogMemory=off
LogTextLocalizationManager=off
LogObj=off
LogExit=off
LogPlatformFile=off
LogOnline=off
LogOnlineGame=off
LogHttp=off
LogSTEOnlineGame=off
LogCircle=off
LogItemGeneratorBase=off
LogBulletHitImpact=off
LogTemp=off
LogScriptPlugin=off
LogUMG=off
LogSecurityInfoCollector=off
AttrModifyComponent=off
LogNearDeath=off
LogSkinnedMeshComp=off
LogNetPartialBunch=off
LogDoor=off
LogBackPack=off
LogPickUp=off
LogIOS=off
LogAndroid=off
LogGCloud=off
LogGameInfo=off
LogNet=off
LogAirAttack=off
LogSTCharacterMovement=off
LogWeaponImportant=off
LogClient=off
LogAvatar=off
LogLandscape=off
LogMidasInterface=off
LogNula=off
LogChangeWearing=off
LogSTCharMoveSpecial=off
LogParticleCache=off
LogVehicle=off
LogVehicleSync=off
LogSkillEditorSkill=off
LogSkillPoolManager=off
LogAIActing=off
LogSTExtraPetCharacter=off
LogCharacterState=off
LogCharacterDamage=off
LogCharParachute=off
LogPetAnimInstance=off
LogPetEventManagerComponent=off
LogNetPlayerMovement=off
LogAntiCheat=off
LogRep=off
LogFPP=off
LogTimeLineSync=off
LogSecurityCoronaLab=off
LogGeneratorItem=off
LogGeneratorTriggerItem=off
LogCharAnim=off
LogParachuteAnimComp=off
LogSTExtraAnimInstance=off
LogSTExtraVehicleAnimInstance=off
LogMonsterAnimInstance=off
LogSimpleAnimList=off
LogInfectionAnimList=off
LogPlayEmote=off
LogLobbyPlayEmoteCom=off
LogActivity=off
LogSpotGroupObject=off
LogFilterConfig=off
LogCharacterParachute=off
MyLandscape=off
PandoraSlua=off
LogSkill=off
LogLevelStreaming=off
LogAkAudio=off
LogGarbage=off
LogTaskTrigger=off
LogWeapon=off
LogWeaponNew=off
LogBackPackComp=off
LogGameplay=off
