<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RestSharp</name>
    </assembly>
    <members>
        <member name="T:RestSharp.Authenticators.HttpBasicAuthenticator">
            <summary>
            Allows "basic access authentication" for HTTP requests.
            </summary>
            <remarks>
            Encoding can be specified depending on what your server expect (see https://stackoverflow.com/a/7243567).
            UTF-8 is used by default but some servers might expect ISO-8859-1 encoding.
            </remarks>
        </member>
        <member name="T:RestSharp.Authenticators.JwtAuthenticator">
            <summary>
            JSON WEB TOKEN (JWT) Authenticator class.
            <remarks>https://tools.ietf.org/html/draft-ietf-oauth-json-web-token</remarks>
            </summary>
        </member>
        <member name="M:RestSharp.Authenticators.JwtAuthenticator.SetBearerToken(System.String)">
            <summary>
            Set the new bearer token so the request gets the new header value
            </summary>
            <param name="accessToken"></param>
        </member>
        <member name="F:RestSharp.Authenticators.OAuth.OAuthTools.UriRfc3986CharsToEscape">
            <summary>
            The set of characters that are unreserved in RFC 2396 but are NOT unreserved in RFC 3986.
            </summary>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetNonce">
            <summary>
            Generates a random 16-byte lowercase alphanumeric string.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetTimestamp">
            <summary>
            Generates a timestamp based on the current elapsed seconds since '01/01/1970 0000 GMT"
            </summary>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetTimestamp(System.DateTime)">
            <summary>
            Generates a timestamp based on the elapsed seconds of a given time since '01/01/1970 0000 GMT"
            </summary>
            <param name="dateTime">A specified point in time.</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.UrlEncodeRelaxed(System.String)">
            <summary>
            URL encodes a string based on section 5.1 of the OAuth spec.
            Namely, percent encoding with [RFC3986], avoiding unreserved characters,
            upper-casing hexadecimal characters, and UTF-8 encoding for text value pairs.
            </summary>
            <param name="value">The value to escape.</param>
            <returns>The escaped value.</returns>
            <remarks>
            The <see cref="M:System.Uri.EscapeDataString(System.String)" /> method is <i>supposed</i> to take on
            RFC 3986 behavior if certain elements are present in a .config file.  Even if this
            actually worked (which in my experiments it <i>doesn't</i>), we can't rely on every
            host actually having this configuration element present.
            </remarks>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.UrlEncodeStrict(System.String)">
            <summary>
            URL encodes a string based on section 5.1 of the OAuth spec.
            Namely, percent encoding with [RFC3986], avoiding unreserved characters,
            upper-casing hexadecimal characters, and UTF-8 encoding for text value pairs.
            </summary>
            <param name="value"></param>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.NormalizeRequestParameters(RestSharp.Authenticators.OAuth.WebPairCollection)">
            <summary>
            Sorts a collection of key-value pairs by name, and then value if equal,
            concatenating them into a single string. This string should be encoded
            prior to, or after normalization is run.
            </summary>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.SortParametersExcludingSignature(RestSharp.Authenticators.OAuth.WebPairCollection)">
            <summary>
            Sorts a <see cref="T:RestSharp.Authenticators.OAuth.WebPairCollection" /> by name, and then value if equal.
            </summary>
            <param name="parameters">A collection of parameters to sort</param>
            <returns>A sorted parameter collection</returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.ConstructRequestUrl(System.Uri)">
            <summary>
            Creates a request URL suitable for making OAuth requests.
            Resulting URLs must exclude port 80 or port 443 when accompanied by HTTP and HTTPS, respectively.
            Resulting URLs must be lower case.
            </summary>
            <param name="url">The original request URL</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.ConcatenateRequestElements(System.String,System.String,RestSharp.Authenticators.OAuth.WebPairCollection)">
            <summary>
            Creates a request elements concatenation value to send with a request.
            This is also known as the signature base.
            </summary>
            <param name="method">The request HTTP method type</param>
            <param name="url">The request URL</param>
            <param name="parameters">The request parameters</param>
            <returns>A signature base string</returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetSignature(RestSharp.Authenticators.OAuth.OAuthSignatureMethod,System.String,System.String)">
            <summary>
            Creates a signature value given a signature base and the consumer secret.
            This method is used when the token secret is currently unknown.
            </summary>
            <param name="signatureMethod">The hashing method</param>
            <param name="signatureBase">The signature base</param>
            <param name="consumerSecret">The consumer key</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetSignature(RestSharp.Authenticators.OAuth.OAuthSignatureMethod,RestSharp.Authenticators.OAuth.OAuthSignatureTreatment,System.String,System.String)">
            <summary>
            Creates a signature value given a signature base and the consumer secret.
            This method is used when the token secret is currently unknown.
            </summary>
            <param name="signatureMethod">The hashing method</param>
            <param name="signatureTreatment">The treatment to use on a signature value</param>
            <param name="signatureBase">The signature base</param>
            <param name="consumerSecret">The consumer key</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetSignature(RestSharp.Authenticators.OAuth.OAuthSignatureMethod,RestSharp.Authenticators.OAuth.OAuthSignatureTreatment,System.String,System.String,System.String)">
            <summary>
            Creates a signature value given a signature base and the consumer secret and a known token secret.
            </summary>
            <param name="signatureMethod">The hashing method</param>
            <param name="signatureTreatment">The treatment to use on a signature value</param>
            <param name="signatureBase">The signature base</param>
            <param name="consumerSecret">The consumer secret</param>
            <param name="tokenSecret">The token secret</param>
            <returns></returns>
        </member>
        <member name="T:RestSharp.Authenticators.OAuth.OAuthWorkflow">
            <summary>
            A class to encapsulate OAuth authentication flow.
            </summary>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthWorkflow.BuildRequestTokenInfo(System.String,RestSharp.Authenticators.OAuth.WebPairCollection)">
            <summary>
            Generates an OAuth signature to pass to an
            <see cref="T:RestSharp.Authenticators.IAuthenticator" /> for the purpose of requesting an
            unauthorized request token.
            </summary>
            <param name="method">The HTTP method for the intended request</param>
            <param name="parameters">Any existing, non-OAuth query parameters desired in the request</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthWorkflow.BuildAccessTokenSignature(System.String,RestSharp.Authenticators.OAuth.WebPairCollection)">
            <summary>
            Generates an OAuth signature to pass to the
            <see cref="T:RestSharp.Authenticators.IAuthenticator" /> for the purpose of exchanging a request token
            for an access token authorized by the user at the Service Provider site.
            </summary>
            <param name="method">The HTTP method for the intended request</param>
            <param name="parameters">Any existing, non-OAuth query parameters desired in the request</param>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthWorkflow.BuildClientAuthAccessTokenSignature(System.String,RestSharp.Authenticators.OAuth.WebPairCollection)">
            <summary>
            Generates an OAuth signature to pass to an
            <see cref="T:RestSharp.Authenticators.IAuthenticator" /> for the purpose of exchanging user credentials
            for an access token authorized by the user at the Service Provider site.
            </summary>
            <param name="method">The HTTP method for the intended request</param>
            <param name="parameters">Any existing, non-OAuth query parameters desired in the request</param>
        </member>
        <member name="T:RestSharp.Authenticators.OAuth1Authenticator">
            <seealso href="http://tools.ietf.org/html/rfc5849">RFC: The OAuth 1.0 Protocol</seealso>
        </member>
        <member name="T:RestSharp.Authenticators.OAuth2.OAuth2AuthorizationRequestHeaderAuthenticator">
            <summary>
            The OAuth 2 authenticator using the authorization request header field.
            </summary>
            <remarks>
            Based on http://tools.ietf.org/html/draft-ietf-oauth-v2-10#section-5.1.1
            </remarks>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth2.OAuth2AuthorizationRequestHeaderAuthenticator.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:RestSharp.Authenticators.OAuth2.OAuth2AuthorizationRequestHeaderAuthenticator" /> class.
            </summary>
            <param name="accessToken">The access token.</param>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth2.OAuth2AuthorizationRequestHeaderAuthenticator.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:RestSharp.Authenticators.OAuth2.OAuth2AuthorizationRequestHeaderAuthenticator" /> class.
            </summary>
            <param name="accessToken">The access token.</param>
            <param name="tokenType">The token type.</param>
        </member>
        <member name="T:RestSharp.Authenticators.OAuth2.OAuth2UriQueryParameterAuthenticator">
            <summary>
            The OAuth 2 authenticator using URI query parameter.
            </summary>
            <remarks>
            Based on http://tools.ietf.org/html/draft-ietf-oauth-v2-10#section-5.1.2
            </remarks>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth2.OAuth2UriQueryParameterAuthenticator.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:RestSharp.Authenticators.OAuth2.OAuth2UriQueryParameterAuthenticator" /> class.
            </summary>
            <param name="accessToken">The access token.</param>
        </member>
        <member name="M:RestSharp.BuildUriExtensions.BuildUri(RestSharp.IRestClient,RestSharp.RestRequest)">
            <summary>
            Builds the URI for the request
            </summary>
            <param name="client">Client instance</param>
            <param name="request">Request instance</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.BuildUriExtensions.BuildUriWithoutQueryParameters(RestSharp.IRestClient,RestSharp.RestRequest)">
            <summary>
            Builds the URI for the request without query parameters.
            </summary>
            <param name="client">Client instance</param>
            <param name="request">Request instance</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.BuildUriExtensions.GetRequestQuery(RestSharp.IRestClient,RestSharp.RestRequest)">
            <summary>
            Gets the query string for the request.
            </summary>
            <param name="client">Client instance</param>
            <param name="request">Request instance</param>
            <returns></returns>
        </member>
        <member name="T:RestSharp.ParameterType">
            <summary>
            Types of parameters that can be added to requests
            </summary>
        </member>
        <member name="F:RestSharp.ParameterType.GetOrPost">
            <summary>
            A <see cref="T:RestSharp.Parameter"/> that will added to the QueryString for GET, DELETE, OPTIONS and HEAD requests; and form for POST and PUT requests.
            </summary>
            <remarks>
            See <see cref="T:RestSharp.GetOrPostParameter"/>.
            </remarks>
        </member>
        <member name="F:RestSharp.ParameterType.UrlSegment">
            <summary>
            A <see cref="T:RestSharp.Parameter"/> that will be added to part of the url by replacing a <c>{placeholder}</c> within the absolute path.
            </summary>
            <remarks>
            See <see cref="T:RestSharp.UrlSegmentParameter"/>.
            </remarks>
        </member>
        <member name="F:RestSharp.ParameterType.HttpHeader">
            <summary>
            A <see cref="T:RestSharp.Parameter"/> that will be added as a request header
            </summary>
            <remarks>
            See <see cref="T:RestSharp.HeaderParameter"/>.
            </remarks>
        </member>
        <member name="F:RestSharp.ParameterType.RequestBody">
            <summary>
            A <see cref="T:RestSharp.Parameter"/> that will be added to the request body
            </summary>
            <remarks>
            See <see cref="T:RestSharp.BodyParameter"/>.
            </remarks>
        </member>
        <member name="F:RestSharp.ParameterType.QueryString">
            <summary>
            A <see cref="T:RestSharp.Parameter"/> that will be added to the query string
            </summary>
            <remarks>
            See <see cref="T:RestSharp.QueryParameter"/>.
            </remarks>
        </member>
        <member name="T:RestSharp.DataFormat">
            <summary>
            Data formats
            </summary>
        </member>
        <member name="T:RestSharp.Method">
            <summary>
            HTTP method to use when making requests
            </summary>
        </member>
        <member name="T:RestSharp.DateFormat">
            <summary>
            Format strings for commonly-used date formats
            </summary>
        </member>
        <member name="F:RestSharp.DateFormat.ISO_8601">
            <summary>
            .NET format string for ISO 8601 date format
            </summary>
        </member>
        <member name="F:RestSharp.DateFormat.ROUND_TRIP">
            <summary>
            .NET format string for roundtrip date format
            </summary>
        </member>
        <member name="T:RestSharp.ResponseStatus">
            <summary>
            Status for responses (surprised?)
            </summary>
        </member>
        <member name="F:RestSharp.ResponseStatus.None">
            <summary>
            Not Applicable, for when the Request has not yet been made
            </summary>
        </member>
        <member name="F:RestSharp.ResponseStatus.Completed">
            <summary>
            <see cref="T:RestSharp.ResponseStatus"/> for when the request is passes as a result of <see cref="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode"/> being true, or when the response is <see cref="F:System.Net.HttpStatusCode.NotFound"/>
            </summary>
        </member>
        <member name="F:RestSharp.ResponseStatus.Error">
            <summary>
            <see cref="T:RestSharp.ResponseStatus"/> for when the request fails due as a result of <see cref="P:System.Net.Http.HttpResponseMessage.IsSuccessStatusCode"/> being false except for the case when the response is <see cref="F:System.Net.HttpStatusCode.NotFound"/>
            </summary>
        </member>
        <member name="F:RestSharp.ResponseStatus.TimedOut">
            <summary>
            <see cref="T:RestSharp.ResponseStatus"/> for when the Operation is cancelled due to the request taking longer than the length of time prescribed by <see cref="P:RestSharp.RestRequest.Timeout"/> or due to the <see cref="T:System.Net.Http.HttpClient"/> timing out.
            </summary>
        </member>
        <member name="F:RestSharp.ResponseStatus.Aborted">
            <summary>
            <see cref="T:RestSharp.ResponseStatus"/> for when the Operation is cancelled, due to reasons other than <see cref="F:RestSharp.ResponseStatus.TimedOut"/>
            </summary>
        </member>
        <member name="T:RestSharp.Extensions.ReflectionExtensions">
            <summary>
            Reflection extensions
            </summary>
        </member>
        <member name="M:RestSharp.Extensions.ReflectionExtensions.GetAttribute``1(System.Reflection.MemberInfo)">
            <summary>
            Retrieve an attribute from a member (property)
            </summary>
            <typeparam name="T">Type of attribute to retrieve</typeparam>
            <param name="prop">Member to retrieve attribute from</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Extensions.ReflectionExtensions.GetAttribute``1(System.Type)">
            <summary>
            Retrieve an attribute from a type
            </summary>
            <typeparam name="T">Type of attribute to retrieve</typeparam>
            <param name="type">Type to retrieve attribute from</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Extensions.ReflectionExtensions.IsSubclassOfRawGeneric(System.Type,System.Type)">
            <summary>
            Checks a type to see if it derives from a raw generic (e.g. List[[]])
            </summary>
            <param name="toCheck"></param>
            <param name="generic"></param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Extensions.ReflectionExtensions.FindEnumValue(System.Type,System.String,System.Globalization.CultureInfo)">
            <summary>
            Find a value from a System.Enum by trying several possible variants
            of the string value of the enum.
            </summary>
            <param name="type">Type of enum</param>
            <param name="value">Value for which to search</param>
            <param name="culture">The culture used to calculate the name variants</param>
            <returns></returns>
        </member>
        <member name="T:RestSharp.Extensions.StreamExtensions">
            <summary>
            Extension method overload!
            </summary>
        </member>
        <member name="M:RestSharp.Extensions.StreamExtensions.ReadAsBytes(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Read a stream into a byte array
            </summary>
            <param name="input">Stream to read</param>
            <param name="cancellationToken"></param>
            <returns>byte[]</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.UrlEncode(System.String)">
            <summary>
            Uses Uri.EscapeDataString() based on recommendations on MSDN
            http://blogs.msdn.com/b/yangxind/archive/2006/11/09/don-t-use-net-system-uri-unescapedatastring-in-url-decoding.aspx
            </summary>
        </member>
        <member name="P:RestSharp.IRestClient.Options">
            <summary>
            Client options that aren't used for configuring HttpClient
            </summary>
        </member>
        <member name="P:RestSharp.IRestClient.Serializers">
            <summary>
            Client-level serializers
            </summary>
        </member>
        <member name="P:RestSharp.IRestClient.DefaultParameters">
            <summary>
            Default parameters to use on every request made with this client instance.
            </summary>
        </member>
        <member name="M:RestSharp.IRestClient.ExecuteAsync(RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Executes the request asynchronously, authenticating if needed
            </summary>
            <param name="request">Request to be executed</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:RestSharp.IRestClient.DownloadStreamAsync(RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            A specialized method to download files as streams.
            </summary>
            <param name="request">Pre-configured request instance.</param>
            <param name="cancellationToken"></param>
            <returns>The downloaded stream.</returns>
        </member>
        <member name="P:RestSharp.RestClientOptions.BaseUrl">
            <summary>
            Base URL for all requests made with this client instance
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.ConfigureMessageHandler">
            <summary>
            Custom configuration for the underlying <seealso cref="T:System.Net.Http.HttpMessageHandler"/>
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.CalculateResponseStatus">
            <summary>
            Function to calculate the response status. By default, the status will be Completed if it was successful, or NotFound.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.Authenticator">
            <summary>
            Authenticator that will be used to populate request with necessary authentication data
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.Credentials">
            <summary>
            Passed to <see cref="T:System.Net.Http.HttpMessageHandler"/> <code>Credentials</code> property
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.UseDefaultCredentials">
            <summary>
            Determine whether or not the "default credentials" (e.g. the user account under which the current process is
            running) will be sent along to the server. The default is false.
            Passed to <see cref="T:System.Net.Http.HttpMessageHandler"/> <code>UseDefaultCredentials</code> property
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.DisableCharset">
            <summary>
            Set to true if you need the Content-Type not to have the charset 
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.AutomaticDecompression">
            <summary>
            Set the decompression method to use when making requests
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.MaxRedirects">
            <summary>
            Set the maximum number of redirects to follow
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.ClientCertificates">
            <summary>
            X509CertificateCollection to be sent with request
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.Proxy">
            <summary>
            Set the proxy to use when making requests. Default is null, which will use the default system proxy if one is set.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.CachePolicy">
            <summary>
            Cache policy to be used for requests using <seealso cref="T:System.Net.Http.Headers.CacheControlHeaderValue"/>
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.FollowRedirects">
            <summary>
            Instruct the client to follow redirects. Default is true.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.Expect100Continue">
            <summary>
            Gets or sets a value that indicates if the <see langword="Expect" /> header for an HTTP request contains Continue.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.UserAgent">
            <summary>
            Value of the User-Agent header to be sent with requests. Default is "RestSharp/{version}"
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.PreAuthenticate">
            <summary>
            Passed to <see cref="T:System.Net.Http.HttpMessageHandler"/> <see langword="PreAuthenticate"/> property
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.RemoteCertificateValidationCallback">
            <summary>
            Callback function for handling the validation of remote certificates. Useful for certificate pinning and
            overriding certificate errors in the scope of a request.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.BaseHost">
            <summary>
            Sets the value of the Host header to be sent with requests.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.CookieContainer">
            <summary>
            Custom cookie container to be used for requests. RestSharp will not assign the container to the message handler,
            but will fetch cookies from it and set them on the request.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.MaxTimeout">
            <summary>
            Maximum request duration in milliseconds. When the request timeout is specified using <seealso cref="P:RestSharp.RestRequest.Timeout"/>,
            the lowest value between the client timeout and request timeout will be used.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.Encoding">
            <summary>
            Default encoding to use when no encoding is specified in the content type header.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.ThrowOnDeserializationError">
            <summary>
            Set to true to throw an exception when a deserialization error occurs. Default is false.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.FailOnDeserializationError">
            <summary>
            When set to true, the response status will be set to <see cref="F:RestSharp.ResponseStatus.Error"/>
            when a deserialization error occurs. Default is true.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.ThrowOnAnyError">
            <summary>
            Set to true to throw an exception when <seealso cref="T:System.Net.Http.HttpClient"/> throws an exception when making a request.
            Default is false.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.AllowMultipleDefaultParametersWithSameName">
            <summary>
            Set to true to allow multiple default parameters with the same name. Default is false.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.Encode">
            <summary>
            Custom function to encode a string for use in a URL.
            </summary>
        </member>
        <member name="P:RestSharp.RestClientOptions.EncodeQuery">
            <summary>
            Custom function to encode a string for use in a URL query.
            </summary>
        </member>
        <member name="P:RestSharp.BodyParameter.DataFormat">
            <summary>
            Body parameter data type
            </summary>
        </member>
        <member name="P:RestSharp.BodyParameter.ContentEncoding">
            <summary>
            Custom content encoding
            </summary>
        </member>
        <member name="M:RestSharp.DefaultParameters.AddParameter(RestSharp.Parameter)">
            <summary>
            Safely add a default parameter to the collection.
            </summary>
            <param name="parameter">Parameter to add</param>
            <returns></returns>
            <exception cref="T:System.NotSupportedException"></exception>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:RestSharp.DefaultParameters.RemoveParameter(System.String,RestSharp.ParameterType)">
            <summary>
            Safely removes all the default parameters with the given name and type.
            </summary>
            <param name="name">Parameter name</param>
            <param name="type">Parameter type</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.DefaultParameters.ReplaceParameter(RestSharp.Parameter)">
            <summary>
            Replace a default parameter with the same name and type.
            </summary>
            <param name="parameter">Parameter instance</param>
            <returns></returns>
        </member>
        <member name="T:RestSharp.FileParameter">
            <summary>
            Container for files to be uploaded with requests
            </summary>
        </member>
        <member name="P:RestSharp.FileParameter.Name">
            <summary>
            Name of the parameter
            </summary>
        </member>
        <member name="P:RestSharp.FileParameter.FileName">
            <summary>
            Name of the file to use when uploading
            </summary>
        </member>
        <member name="P:RestSharp.FileParameter.ContentType">
            <summary>
            MIME content type of file
            </summary>
        </member>
        <member name="P:RestSharp.FileParameter.GetFile">
            <summary>
            Provides raw data for file
            </summary>
        </member>
        <member name="M:RestSharp.FileParameter.Create(System.String,System.Byte[],System.String,RestSharp.ContentType,RestSharp.FileParameterOptions)">
            <summary>
            Creates a file parameter from an array of bytes.
            </summary>
            <param name="name">The parameter name to use in the request.</param>
            <param name="data">The data to use as the file's contents.</param>
            <param name="filename">The filename to use in the request.</param>
            <param name="contentType">The content type to use in the request.</param>
            <param name="options">File parameter options</param>
            <returns>The <see cref="T:RestSharp.FileParameter" /></returns>
        </member>
        <member name="M:RestSharp.FileParameter.Create(System.String,System.Func{System.IO.Stream},System.String,RestSharp.ContentType,RestSharp.FileParameterOptions)">
            <summary>
            Creates a file parameter from an array of bytes.
            </summary>
            <param name="name">The parameter name to use in the request.</param>
            <param name="getFile">Delegate that will be called with the request stream so you can write to it..</param>
            <param name="fileName">The filename to use in the request.</param>
            <param name="contentType">Optional: parameter content type, default is "application/g-zip"</param>
            <param name="options">File parameter options</param>
            <returns>The <see cref="T:RestSharp.FileParameter" /> using the default content type.</returns>
        </member>
        <member name="M:RestSharp.GetOrPostParameter.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            Instantiates an HTTP parameter instance (QueryString for GET, DELETE, OPTIONS and HEAD; Encoded form for POST and PUT)
            </summary>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <param name="encode">Encode the value or not, default true</param>
        </member>
        <member name="M:RestSharp.HeaderParameter.#ctor(System.String,System.String)">
            <summary>
            Instantiates a header parameter
            </summary>
            <param name="name">Parameter name</param>
            <param name="value">Parameter value</param>
        </member>
        <member name="T:RestSharp.Parameter">
            <summary>
            Parameter container for REST requests
            </summary>
        </member>
        <member name="M:RestSharp.Parameter.#ctor(System.String,System.Object,RestSharp.ParameterType,System.Boolean)">
            <summary>
            Parameter container for REST requests
            </summary>
        </member>
        <member name="P:RestSharp.Parameter.ContentType">
            <summary>
            MIME content type of the parameter
            </summary>
        </member>
        <member name="M:RestSharp.Parameter.ToString">
            <summary>
            Return a human-readable representation of this parameter
            </summary>
            <returns>String</returns>
        </member>
        <member name="M:RestSharp.QueryParameter.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            Instantiates a new query parameter instance that will be added to the request URL as {name}={value} part of the query string.
            </summary>
            <param name="name">Parameter name</param>
            <param name="value">Parameter value</param>
            <param name="encode">Optional: encode the value, default is true</param>
        </member>
        <member name="M:RestSharp.UrlSegmentParameter.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            Instantiates a new query parameter instance that will be added to the request URL by replacing part of the absolute path.
            The request resource should have a placeholder {name} that will be replaced with the parameter value when the request is made.
            </summary>
            <param name="name">Parameter name</param>
            <param name="value">Parameter value</param>
            <param name="encode">Optional: encode the value, default is true</param>
        </member>
        <member name="M:RestSharp.PropertyCache`1.GetParameters(`0,System.String[])">
            <summary>
            Gets parameters from the provided object
            </summary>
            <param name="entity">The object from which to get the parameters</param>
            <param name="includedProperties">Properties to include, or nothing to include everything. The array will be sorted.</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.PropertyCache`1.GetParameters(`0)">
            <summary>
            Gets parameters from the provided object
            </summary>
            <param name="entity">The object from which to get the parameters</param>
            <returns></returns>
        </member>
        <member name="P:RestSharp.PropertyCache`1.Populator.PropertyName">
            <summary>
            Gets the name of the property this populator represents.
            </summary>
            <remarks>
            This corresponds to the actual property name and not the name
            determined by <see cref="P:RestSharp.RequestPropertyAttribute.Name"/>
            </remarks>
        </member>
        <member name="M:RestSharp.PropertyCache`1.Populator.Populate(`0,System.Collections.Generic.ICollection{RestSharp.Parameter})">
            <summary>
            Populates the provided parameters collection
            </summary>
            <param name="entity">The object to get parameters from</param>
            <param name="parameters">The parameters collection to populate</param>
        </member>
        <member name="M:RestSharp.PropertyCache`1.Populator.From(System.Reflection.PropertyInfo)">
            <summary>
            Creates a new populator instance from the provided property
            </summary>
            <param name="property">A public instance property from the <typeparamref name="T"/> type</param>
            <returns></returns>
        </member>
        <member name="P:RestSharp.PropertyCache`1.Populator.RequestProperty.Name">
            <summary>
            Gets or sets the <see cref="P:RestSharp.RequestPropertyAttribute.Name"/> associated
            with the property this object represents
            </summary>
        </member>
        <member name="P:RestSharp.PropertyCache`1.Populator.RequestProperty.Format">
            <summary>
            Gets the <see cref="P:RestSharp.RequestPropertyAttribute.Format"/> associated with
            the property this object represents
            </summary>
        </member>
        <member name="P:RestSharp.PropertyCache`1.Populator.RequestProperty.ArrayQueryType">
            <summary>
            Gets the <see cref="P:RestSharp.RequestPropertyAttribute.ArrayQueryType"/> associated
            with the property this object represents
            </summary>
        </member>
        <member name="P:RestSharp.PropertyCache`1.Populator.RequestProperty.Type">
            <summary>
            Gets the return type of the property this object represents
            </summary>
        </member>
        <member name="M:RestSharp.PropertyCache`1.Populator.RequestProperty.From(System.Reflection.PropertyInfo)">
            <summary>
            Creates a new request property representation of the provided property
            </summary>
            <param name="property">The property to turn into a request property</param>
            <returns></returns>
        </member>
        <member name="T:RestSharp.RestRequest">
            <summary>
            Container for data used to make requests
            </summary>
        </member>
        <member name="M:RestSharp.RestRequest.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:RestSharp.RestRequest.#ctor(System.String,RestSharp.Method)">
            <summary>
            Constructor for a rest request to a relative resource URL and optional method
            </summary>
            <param name="resource">Resource to use</param>
            <param name="method">Method to use (defaults to Method.Get></param>
        </member>
        <member name="M:RestSharp.RestRequest.#ctor(System.Uri,RestSharp.Method)">
            <summary>
            Constructor for a rest request to a specific resource Uri and optional method
            </summary>
            <param name="resource">Resource Uri to use</param>
            <param name="method">Method to use (defaults to Method.Get></param>
        </member>
        <member name="P:RestSharp.RestRequest.AlwaysMultipartFormData">
            <summary>
            Always send a multipart/form-data request - even when no Files are present.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.MultipartFormQuoteParameters">
            <summary>
            When set to true, parameter values in a multipart form data requests will be enclosed in
            quotation marks. Default is false. Enable it if the remote endpoint requires parameters
            to be in quotes (for example, FreshDesk API). 
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.MultipartFormQuoteBoundary">
            <summary>
            When set to true, the form boundary part of the content type will be enclosed in
            quotation marks. Default is true.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.FormBoundary">
            <summary>
            Overrides the default (random) form boundary
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Parameters">
            <summary>
            Container of all HTTP parameters to be passed with the request.
            See AddParameter() for explanation of the types of parameters that can be passed
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.CookieContainer">
            <summary>
            Optional cookie container to use for the request. If not set, cookies are not passed.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Authenticator">
            <summary>
            Request-level authenticator. It will be used if set, otherwise RestClient.Authenticator will be used.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Files">
            <summary>
            Container of all the files to be uploaded with the request.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Method">
            <summary>
            Determines what HTTP method to use for this request. Supported methods: GET, POST, PUT, DELETE, HEAD, OPTIONS
            Default is GET
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Timeout">
            <summary>
            Custom request timeout
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Resource">
            <summary>
            The Resource URL to make the request against.
            Tokens are substituted with UrlSegment parameters and match by name.
            Should not include the scheme or domain. Do not include leading slash.
            Combined with RestClient.BaseUrl to assemble final URL:
            {BaseUrl}/{Resource} (BaseUrl is scheme + domain, e.g. http://example.com)
            </summary>
            <example>
            // example for url token replacement
            request.Resource = "Products/{ProductId}";
            request.AddParameter("ProductId", 123, ParameterType.UrlSegment);
            </example>
        </member>
        <member name="P:RestSharp.RestRequest.RequestFormat">
            <summary>
            Serializer to use when writing request bodies.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.RootElement">
            <summary>
            Used by the default deserializers to determine where to start deserializing from.
            Can be used to skip container or root elements that do not have corresponding deserialzation targets.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.OnBeforeDeserialization">
            <summary>
            When supplied, the function will be called before calling the deserializer
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.OnBeforeRequest">
            <summary>
            When supplied, the function will be called before making a request
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.OnAfterRequest">
            <summary>
            When supplied, the function will be called after the request is complete
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Attempts">
            <summary>
            How many attempts were made to send this Request
            </summary>
            <remarks>
            This number is incremented each time the RestClient sends the request.
            </remarks>
        </member>
        <member name="P:RestSharp.RestRequest.CompletionOption">
            <summary>
            Completion option for <seealso cref="T:System.Net.Http.HttpClient"/>
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.CachePolicy">
            <summary>
            Cache policy to be used for requests using <seealso cref="T:System.Net.Http.Headers.CacheControlHeaderValue"/>
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.ResponseWriter">
            <summary>
            Set this to write response to Stream rather than reading into memory.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.AdvancedResponseWriter">
            <summary>
            Set this to handle the response stream yourself, based on the response details
            </summary>
        </member>
        <member name="M:RestSharp.RestRequest.AddParameter(RestSharp.Parameter)">
            <summary>
            Adds a parameter object to the request parameters
            </summary>
            <param name="parameter">Parameter to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequest.RemoveParameter(RestSharp.Parameter)">
            <summary>
            Removes a parameter object from the request parameters
            </summary>
            <param name="parameter">Parameter to remove</param>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddParameter(RestSharp.RestRequest,System.String,System.String,System.Boolean)">
            <summary>
            Adds a HTTP parameter to the request (QueryString for GET, DELETE, OPTIONS and HEAD; Encoded form for POST and PUT)
            </summary>
            <param name="request"></param>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <param name="encode">Encode the value or not, default true</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddParameter``1(RestSharp.RestRequest,System.String,``0,System.Boolean)">
            <summary>
            Adds a HTTP parameter to the request (QueryString for GET, DELETE, OPTIONS and HEAD; Encoded form for POST and PUT)
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <param name="encode">Encode the value or not, default true</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddOrUpdateParameter(RestSharp.RestRequest,System.String,System.String,System.Boolean)">
            <summary>
            Adds or updates a HTTP parameter to the request (QueryString for GET, DELETE, OPTIONS and HEAD; Encoded form for POST and PUT)
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <param name="encode">Encode the value or not, default true</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddOrUpdateParameter``1(RestSharp.RestRequest,System.String,``0,System.Boolean)">
            <summary>
            Adds or updates a HTTP parameter to the request (QueryString for GET, DELETE, OPTIONS and HEAD; Encoded form for POST and PUT)
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <param name="encode">Encode the value or not, default true</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddUrlSegment(RestSharp.RestRequest,System.String,System.String,System.Boolean)">
            <summary>
            Adds a URL segment parameter to the request. The resource URL must have a placeholder for the parameter for it to work.
            For example, if you add a URL segment parameter with the name "id", the resource URL should contain {id} in its path.
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Name of the parameter, must be matching a placeholder in the resource URL as {name}</param>
            <param name="value">Value of the parameter</param>
            <param name="encode">Encode the value or not, default true</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddUrlSegment``1(RestSharp.RestRequest,System.String,``0,System.Boolean)">
            <summary>
            Adds a URL segment parameter to the request. The resource URL must have a placeholder for the parameter for it to work.
            For example, if you add a URL segment parameter with the name "id", the resource URL should contain {id} in its path.
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Name of the parameter, must be matching a placeholder in the resource URL as {name}</param>
            <param name="value">Value of the parameter</param>
            <param name="encode">Encode the value or not, default true</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddQueryParameter(RestSharp.RestRequest,System.String,System.String,System.Boolean)">
            <summary>
            Adds a query string parameter to the request. The request resource should not contain any placeholders for this parameter.
            The parameter will be added to the request URL as a query string using name=value format.
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Parameter name</param>
            <param name="value">Parameter value</param>
            <param name="encode">Encode the value or not, default true</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddQueryParameter``1(RestSharp.RestRequest,System.String,``0,System.Boolean)">
            <summary>
            Adds a query string parameter to the request. The request resource should not contain any placeholders for this parameter.
            The parameter will be added to the request URL as a query string using name=value format.
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Parameter name</param>
            <param name="value">Parameter value</param>
            <param name="encode">Encode the value or not, default true</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddHeader(RestSharp.RestRequest,System.String,System.String)">
            <summary>
            Adds a header to the request. RestSharp will try to separate request and content headers when calling the resource.
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Header name</param>
            <param name="value">Header value</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddHeader``1(RestSharp.RestRequest,System.String,``0)">
            <summary>
            Adds a header to the request. RestSharp will try to separate request and content headers when calling the resource.
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Header name</param>
            <param name="value">Header value</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddOrUpdateHeader(RestSharp.RestRequest,System.String,System.String)">
            <summary>
            Adds or updates the request header. RestSharp will try to separate request and content headers when calling the resource.
            Existing header with the same name will be replaced.
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Header name</param>
            <param name="value">Header value</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddOrUpdateHeader``1(RestSharp.RestRequest,System.String,``0)">
            <summary>
            Adds or updates the request header. RestSharp will try to separate request and content headers when calling the resource.
            Existing header with the same name will be replaced.
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Header name</param>
            <param name="value">Header value</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddHeaders(RestSharp.RestRequest,System.Collections.Generic.ICollection{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Adds multiple headers to the request, using the key-value pairs provided.
            </summary>
            <param name="request">Request instance</param>
            <param name="headers">Collection of key-value pairs, where key will be used as header name, and value as header value</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddOrUpdateHeaders(RestSharp.RestRequest,System.Collections.Generic.ICollection{System.Collections.Generic.KeyValuePair{System.String,System.String}})">
            <summary>
            Adds or updates multiple headers to the request, using the key-value pairs provided. Existing headers with the same name will be replaced.
            </summary>
            <param name="request">Request instance</param>
            <param name="headers">Collection of key-value pairs, where key will be used as header name, and value as header value</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddParameter(RestSharp.RestRequest,System.String,System.Object,RestSharp.ParameterType,System.Boolean)">
            <summary>
            Adds a parameter of a given type to the request. It will create a typed parameter instance based on the type argument.
            It is not recommended to use this overload unless you must, as it doesn't provide any restrictions, and if the name-value-type
            combination doesn't match, it will throw.
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Name of the parameter, must be matching a placeholder in the resource URL as {name}</param>
            <param name="value">Value of the parameter</param>
            <param name="type">Enum value specifying what kind of parameter is being added</param>
            <param name="encode">Encode the value or not, default true</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddOrUpdateParameter(RestSharp.RestRequest,System.String,System.Object,RestSharp.ParameterType,System.Boolean)">
            <summary>
            Adds or updates request parameter of a given type. It will create a typed parameter instance based on the type argument.
            Parameter will be added or updated based on its name. If the request has a parameter with the same name, it will be updated.
            It is not recommended to use this overload unless you must, as it doesn't provide any restrictions, and if the name-value-type
            combination doesn't match, it will throw.
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Name of the parameter, must be matching a placeholder in the resource URL as {name}</param>
            <param name="value">Value of the parameter</param>
            <param name="type">Enum value specifying what kind of parameter is being added</param>
            <param name="encode">Encode the value or not, default true</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddOrUpdateParameter(RestSharp.RestRequest,RestSharp.Parameter)">
            <summary>
            Adds or updates request parameter, given the parameter instance, for example <see cref="T:RestSharp.QueryParameter"/> or <see cref="T:RestSharp.UrlSegmentParameter"/>.
            It will replace an existing parameter with the same name.
            </summary>
            <param name="request">Request instance</param>
            <param name="parameter">Parameter instance</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddOrUpdateParameters(RestSharp.RestRequest,System.Collections.Generic.IEnumerable{RestSharp.Parameter})">
            <summary>
            Adds or updates multiple request parameters, given the parameter instance, for example
            <see cref="T:RestSharp.QueryParameter"/> or <see cref="T:RestSharp.UrlSegmentParameter"/>. Parameters with the same name will be replaced.
            </summary>
            <param name="request">Request instance</param>
            <param name="parameters">Collection of parameter instances</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddFile(RestSharp.RestRequest,System.String,System.String,RestSharp.ContentType,RestSharp.FileParameterOptions)">
            <summary>
            Adds a file parameter to the request body. The file will be read from disk as a stream.
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Parameter name</param>
            <param name="path">Full path to the file</param>
            <param name="contentType">Optional: content type</param>
            <param name="options">File parameter header options</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddFile(RestSharp.RestRequest,System.String,System.Byte[],System.String,RestSharp.ContentType,RestSharp.FileParameterOptions)">
            <summary>
            Adds bytes to the request as file attachment
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Parameter name</param>
            <param name="bytes">File content as bytes</param>
            <param name="fileName">File name</param>
            <param name="contentType">Optional: content type. Default is "application/octet-stream"</param>
            <param name="options">File parameter header options</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddFile(RestSharp.RestRequest,System.String,System.Func{System.IO.Stream},System.String,RestSharp.ContentType,RestSharp.FileParameterOptions)">
            <summary>
            Adds a file attachment to the request, where the file content will be retrieved from a given stream
            </summary>
            <param name="request">Request instance</param>
            <param name="name">Parameter name</param>
            <param name="getFile">Function that returns a stream with the file content</param>
            <param name="fileName">File name</param>
            <param name="contentType">Optional: content type. Default is "application/octet-stream"</param>
            <param name="options">File parameter header options</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddBody(RestSharp.RestRequest,System.Object,RestSharp.ContentType)">
            <summary>
            Adds a body parameter to the request
            </summary>
            <param name="request">Request instance</param>
            <param name="obj">Object to be used as the request body, or string for plain content</param>
            <param name="contentType">Optional: content type</param>
            <returns></returns>
            <exception cref="T:System.ArgumentException">Thrown if request body type cannot be resolved</exception>
            <remarks>This method will try to figure out the right content type based on the request data format and the provided content type</remarks>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddStringBody(RestSharp.RestRequest,System.String,RestSharp.DataFormat)">
            <summary>
            Adds a string body and figures out the content type from the data format specified. You can, for example, add a JSON string
            using this method as request body, using DataFormat.Json/>
            </summary>
            <param name="request">Request instance</param>
            <param name="body">String body</param>
            <param name="dataFormat"><see cref="T:RestSharp.DataFormat"/> for the content</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddStringBody(RestSharp.RestRequest,System.String,RestSharp.ContentType)">
            <summary>
            Adds a string body to the request using the specified content type.
            </summary>
            <param name="request">Request instance</param>
            <param name="body">String body</param>
            <param name="contentType">Content type of the body</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddJsonBody(RestSharp.RestRequest,System.String,System.Boolean,RestSharp.ContentType)">
            <summary>
            Adds a JSON body parameter to the request from a string
            </summary>
            <param name="request">Request instance</param>
            <param name="forceSerialize">Force serialize the top-level string</param>
            <param name="contentType">Optional: content type. Default is "application/json"</param>
            <param name="jsonString">JSON string to be used as a body</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddJsonBody``1(RestSharp.RestRequest,``0,RestSharp.ContentType)">
            <summary>
            Adds a JSON body parameter to the request
            </summary>
            <param name="request">Request instance</param>
            <param name="obj">Object that will be serialized to JSON</param>
            <param name="contentType">Optional: content type. Default is "application/json"</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddXmlBody``1(RestSharp.RestRequest,``0,RestSharp.ContentType,System.String)">
            <summary>
            Adds an XML body parameter to the request
            </summary>
            <param name="request">Request instance</param>
            <param name="obj">Object that will be serialized to XML</param>
            <param name="contentType">Optional: content type. Default is "application/xml"</param>
            <param name="xmlNamespace">Optional: XML namespace</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddObject``1(RestSharp.RestRequest,``0,System.String[])">
            <summary>
            Gets object properties and adds each property as a form data parameter
            </summary>
            <param name="request">Request instance</param>
            <param name="obj">Object to add as form data</param>
            <param name="includedProperties">Properties to include, or nothing to include everything. The array will be sorted.</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddObjectStatic``1(RestSharp.RestRequest,``0,System.String[])">
            <summary>
            Gets object properties and adds each property as a form data parameter
            </summary>
            <remarks>
            This method gets public instance properties from the provided <typeparamref name="T"/> type
            rather than from <paramref name="obj"/> itself, which allows for caching of properties and
            other optimizations. If you don't know the type at runtime, or wish to use properties not
            available from the provided type parameter, consider using <see cref="M:RestSharp.RestRequestExtensions.AddObject``1(RestSharp.RestRequest,``0,System.String[])"/>
            </remarks>
            <param name="request">Request instance</param>
            <param name="obj">Object to add as form data</param>
            <param name="includedProperties">Properties to include, or nothing to include everything. The array will be sorted.</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddObjectStatic``1(RestSharp.RestRequest,``0)">
            <summary>
            Gets object properties and adds each property as a form data parameter
            </summary>
            <remarks>
            This method gets public instance properties from the provided <typeparamref name="T"/> type
            rather than from <paramref name="obj"/> itself, which allows for caching of properties and
            other optimizations. If you don't know the type at runtime, or wish to use properties not
            available from the provided type parameter, consider using <see cref="M:RestSharp.RestRequestExtensions.AddObject``1(RestSharp.RestRequest,``0,System.String[])"/>
            </remarks>
            <param name="request">Request instance</param>
            <param name="obj">Object to add as form data</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequestExtensions.AddCookie(RestSharp.RestRequest,System.String,System.String,System.String,System.String)">
            <summary>
            Adds cookie to the <seealso cref="T:System.Net.Http.HttpClient"/> cookie container.
            </summary>
            <param name="request">RestRequest to add the cookies to</param>
            <param name="name">Cookie name</param>
            <param name="value">Cookie value</param>
            <param name="path">Cookie path</param>
            <param name="domain">Cookie domain, must not be an empty string</param>
            <returns></returns>
        </member>
        <member name="P:RestSharp.RestXmlRequest.DateFormat">
            <summary>
            Used by the default deserializers to explicitly set which date format string to use when parsing dates.
            </summary>
        </member>
        <member name="P:RestSharp.RestXmlRequest.XmlNamespace">
            <summary>
            Used by XmlDeserializer. If not specified, XmlDeserializer will flatten response by removing namespaces from
            element names.
            </summary>
        </member>
        <member name="T:RestSharp.RestResponse`1">
            <summary>
            Container for data sent back from API including deserialized data
            </summary>
            <typeparam name="T">Type of data to deserialize to</typeparam>
        </member>
        <member name="P:RestSharp.RestResponse`1.Data">
            <summary>
            Deserialized entity data
            </summary>
        </member>
        <member name="T:RestSharp.RestResponse">
            <summary>
            Container for data sent back from API
            </summary>
        </member>
        <member name="T:RestSharp.RestResponseBase">
            <summary>
            Base class for common properties shared by RestResponse and RestResponse[[T]]
            </summary>
        </member>
        <member name="M:RestSharp.RestResponseBase.#ctor(RestSharp.RestRequest)">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.Request">
            <summary>
            The RestRequest that was made to get this RestResponse
            </summary>
            <remarks>
            Mainly for debugging if ResponseStatus is not OK
            </remarks>
        </member>
        <member name="P:RestSharp.RestResponseBase.ContentType">
            <summary>
            MIME content type of response
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ContentLength">
            <summary>
            Length in bytes of the response content
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ContentEncoding">
            <summary>
            Encoding of the response content
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.Content">
            <summary>
            String representation of response content
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.StatusCode">
            <summary>
            HTTP response status code
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.IsSuccessStatusCode">
            <summary>
            Whether or not the HTTP response status code indicates success
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.IsSuccessful">
            <summary>
            Whether or not the HTTP response status code indicates success and no other error occurred (deserialization, timeout, ...)
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.StatusDescription">
            <summary>
            Description of HTTP status returned
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.RawBytes">
            <summary>
            Response content
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ResponseUri">
            <summary>
            The URL that actually responded to the content (different from request if redirected)
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.Server">
            <summary>
            HttpWebResponse.Server
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.Cookies">
            <summary>
            Cookies returned by server with the response
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.Headers">
            <summary>
            Response headers returned by server with the response
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ContentHeaders">
            <summary>
            Content headers returned by server with the response
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ResponseStatus">
            <summary>
            Status of the request. Will return Error for transport errors.
            HTTP errors will still return ResponseStatus.Completed, check StatusCode instead
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ErrorMessage">
            <summary>
            Transport or other non-HTTP error generated while attempting request
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ErrorException">
            <summary>
            The exception thrown during the request, if any
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.Version">
            <summary>
            HTTP protocol version of the request
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.RootElement">
            <summary>
            Root element of the serialized response content, only works if deserializer supports it 
            </summary>
        </member>
        <member name="M:RestSharp.RestResponseBase.DebuggerDisplay">
            <summary>
            Assists with debugging responses by displaying in the debugger output
            </summary>
            <returns></returns>
        </member>
        <member name="T:RestSharp.RestClient">
            <summary>
            Client to translate RestRequests into Http requests and process response result
            </summary>
        </member>
        <member name="M:RestSharp.RestClient.ExecuteAsync(RestSharp.RestRequest,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:RestSharp.RestClient.DownloadStreamAsync(RestSharp.RestRequest,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="P:RestSharp.RestClient.AcceptedContentTypes">
            <summary>
            Content types that will be sent in the Accept header. The list is populated from the known serializers.
            If you need to send something else by default, set this property to a different value.
            </summary>
        </member>
        <member name="P:RestSharp.RestClient.Options">
            <inheritdoc />>
        </member>
        <member name="P:RestSharp.RestClient.Serializers">
            <inheritdoc />>
        </member>
        <member name="P:RestSharp.RestClient.DefaultParameters">
            <inheritdoc/>
        </member>
        <member name="M:RestSharp.RestClient.#ctor(RestSharp.RestClientOptions,RestSharp.ConfigureHeaders,RestSharp.ConfigureSerialization,System.Boolean)">
            <summary>
            Creates an instance of RestClient using the provided <see cref="T:RestSharp.RestClientOptions"/>
            </summary>
            <param name="options">Client options</param>
            <param name="configureDefaultHeaders">Delegate to add default headers to the wrapped HttpClient instance</param>
            <param name="configureSerialization">Delegate to configure serialization</param>
            <param name="useClientFactory">Set to true if you wish to reuse the <seealso cref="P:RestSharp.RestClient.HttpClient"/> instance</param>
        </member>
        <member name="M:RestSharp.RestClient.#ctor(RestSharp.ConfigureRestClient,RestSharp.ConfigureHeaders,RestSharp.ConfigureSerialization,System.Boolean)">
            <summary>
            Creates an instance of RestClient using the default <see cref="T:RestSharp.RestClientOptions"/>
            </summary>
            <param name="configureRestClient">Delegate to configure the client options</param>
            <param name="configureDefaultHeaders">Delegate to add default headers to the wrapped HttpClient instance</param>
            <param name="configureSerialization">Delegate to configure serialization</param>
            <param name="useClientFactory">Set to true if you wish to reuse the <seealso cref="P:RestSharp.RestClient.HttpClient"/> instance</param>
        </member>
        <member name="M:RestSharp.RestClient.#ctor(System.Uri,RestSharp.ConfigureRestClient,RestSharp.ConfigureHeaders,RestSharp.ConfigureSerialization,System.Boolean)">
            <inheritdoc />
            <summary>
            Creates an instance of RestClient using a specific BaseUrl for requests made by this client instance
            </summary>
            <param name="baseUrl">Base URI for the new client</param>
            <param name="configureRestClient">Delegate to configure the client options</param>
            <param name="configureDefaultHeaders">Delegate to add default headers to the wrapped HttpClient instance</param>
            <param name="configureSerialization">Delegate to configure serialization</param>
            <param name="useClientFactory">Set to true if you wish to reuse the <seealso cref="P:RestSharp.RestClient.HttpClient"/> instance</param>
        </member>
        <member name="M:RestSharp.RestClient.#ctor(System.String,RestSharp.ConfigureRestClient,RestSharp.ConfigureHeaders,RestSharp.ConfigureSerialization)">
            <summary>
            Creates an instance of RestClient using a specific BaseUrl for requests made by this client instance
            </summary>
            <param name="baseUrl">Base URI for this new client as a string</param>
            <param name="configureRestClient">Delegate to configure the client options</param>
            <param name="configureDefaultHeaders">Delegate to add default headers to the wrapped HttpClient instance</param>
            <param name="configureSerialization">Delegate to configure serialization</param>
        </member>
        <member name="M:RestSharp.RestClient.#ctor(System.Net.Http.HttpClient,RestSharp.RestClientOptions,System.Boolean,RestSharp.ConfigureSerialization)">
            <summary>
            Creates an instance of RestClient using a shared HttpClient and specific RestClientOptions and does not allocate one internally.
            </summary>
            <param name="httpClient">HttpClient to use</param>
            <param name="options">RestClient options to use</param>
            <param name="disposeHttpClient">True to dispose of the client, false to assume the caller does (defaults to false)</param>
            <param name="configureSerialization">Delegate to configure serialization</param>
        </member>
        <member name="M:RestSharp.RestClient.#ctor(System.Net.Http.HttpClient,System.Boolean,RestSharp.ConfigureRestClient,RestSharp.ConfigureSerialization)">
            <summary>
            Creates an instance of RestClient using a shared HttpClient and does not allocate one internally.
            </summary>
            <param name="httpClient">HttpClient to use</param>
            <param name="disposeHttpClient">True to dispose of the client, false to assume the caller does (defaults to false)</param>
            <param name="configureRestClient">Delegate to configure the client options</param>
            <param name="configureSerialization">Delegate to configure serialization</param>
        </member>
        <member name="M:RestSharp.RestClient.#ctor(System.Net.Http.HttpMessageHandler,System.Boolean,RestSharp.ConfigureRestClient,RestSharp.ConfigureSerialization)">
            <summary>
            Creates a new instance of RestSharp using the message handler provided. By default, HttpClient disposes the provided handler
            when the client itself is disposed. If you want to keep the handler not disposed, set disposeHandler argument to false.
            </summary>
            <param name="handler">Message handler instance to use for HttpClient</param>
            <param name="disposeHandler">Dispose the handler when disposing RestClient, true by default</param>
            <param name="configureRestClient">Delegate to configure the client options</param>
            <param name="configureSerialization">Delegate to configure serialization</param>
        </member>
        <member name="M:RestSharp.RestClientExtensions.ExecuteGetAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Executes a GET-style request asynchronously, authenticating if needed.
            The response content then gets deserialized to T.
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Deserialized response content</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.ExecuteGetAsync(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Executes a GET-style asynchronously, authenticating if needed
            </summary>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:RestSharp.RestClientExtensions.ExecutePostAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Executes a POST-style request asynchronously, authenticating if needed.
            The response content then gets deserialized to T.
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="cancellationToken">The cancellation token</param>
            <returns>Deserialized response content</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.ExecutePostAsync(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Executes a POST-style asynchronously, authenticating if needed
            </summary>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:RestSharp.RestClientExtensions.ExecutePutAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Executes a PUT-style request asynchronously, authenticating if needed.
            The response content then gets deserialized to T.
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="cancellationToken">The cancellation token</param>
            <returns>Deserialized response content</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.ExecutePutAsync(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Executes a PUP-style asynchronously, authenticating if needed
            </summary>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:RestSharp.RestClientExtensions.ExecuteAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Executes the request asynchronously, authenticating if needed
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:RestSharp.RestClientExtensions.ExecuteAsync(RestSharp.IRestClient,RestSharp.RestRequest,RestSharp.Method,System.Threading.CancellationToken)">
            <summary>
            Executes the request asynchronously, authenticating if needed
            </summary>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="httpMethod">Override the request method</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:RestSharp.RestClientExtensions.ExecuteAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,RestSharp.Method,System.Threading.CancellationToken)">
            <summary>
            Executes the request asynchronously, authenticating if needed
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="httpMethod">Override the request method</param>
            <param name="cancellationToken">Cancellation token</param>
        </member>
        <member name="M:RestSharp.RestClientExtensions.GetAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Execute the request using GET HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.PostAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Execute the request using POST HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.PutAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Execute the request using PUT HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.HeadAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Execute the request using HEAD HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.OptionsAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Execute the request using OPTIONS HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.PatchAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Execute the request using PATCH HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.DeleteAsync``1(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Execute the request using DELETE HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.DownloadDataAsync(RestSharp.IRestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            A specialized method to download files.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">Pre-configured request instance.</param>
            <param name="cancellationToken"></param>
            <returns>The downloaded file.</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.StreamJsonAsync``1(RestSharp.IRestClient,System.String,System.Threading.CancellationToken)">
            <summary>
            Reads a stream returned by the specified endpoint, deserializes each line to JSON and returns each object asynchronously.
            It is required for each JSON object to be returned in a single line.
            </summary>
            <param name="client"></param>
            <param name="resource"></param>
            <param name="cancellationToken"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.GetJsonAsync``1(RestSharp.RestClient,System.String,System.Threading.CancellationToken)">
            <summary>
            Calls the URL specified in the <code>resource</code> parameter, expecting a JSON response back. Deserializes and returns the response.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="TResponse">Response object type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.GetJsonAsync``1(RestSharp.RestClient,System.String,System.Object,System.Threading.CancellationToken)">
            <summary>
            Calls the URL specified in the <code>resource</code> parameter, expecting a JSON response back. Deserializes and returns the response.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <param name="parameters">Parameters to pass to the request</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="TResponse">Response object type</typeparam>
            <returns>Deserialized response object</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.PostJsonAsync``2(RestSharp.RestClient,System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Serializes the <code>request</code> object to JSON and makes a POST call to the resource specified in the <code>resource</code> parameter.
            Expects a JSON response back, deserializes it to <code>TResponse</code> type and returns it.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <param name="request">Request object, must be serializable to JSON</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="TRequest">Request object type</typeparam>
            <typeparam name="TResponse">Response object type</typeparam>
            <returns>Deserialized response object</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.PostJsonAsync``1(RestSharp.RestClient,System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Serializes the <code>request</code> object to JSON and makes a POST call to the resource specified in the <code>resource</code> parameter.
            Expects no response back, just the status code.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <param name="request">Request object, must be serializable to JSON</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="TRequest">Request object type</typeparam>
            <returns>Response status code</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.PutJsonAsync``2(RestSharp.RestClient,System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Serializes the <code>request</code> object to JSON and makes a PUT call to the resource specified in the <code>resource</code> parameter.
            Expects a JSON response back, deserializes it to <code>TResponse</code> type and returns it.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <param name="request">Request object, must be serializable to JSON</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="TRequest">Request object type</typeparam>
            <typeparam name="TResponse">Response object type</typeparam>
            <returns>Deserialized response object</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.PutJsonAsync``1(RestSharp.RestClient,System.String,``0,System.Threading.CancellationToken)">
            <summary>
            Serializes the <code>request</code> object to JSON and makes a PUT call to the resource specified in the <code>resource</code> parameter.
            Expects no response back, just the status code.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <param name="request">Request object, must be serializable to JSON</param>
            <param name="cancellationToken">Cancellation token</param>
            <typeparam name="TRequest">Request object type</typeparam>
            <returns>Response status code</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultParameter(RestSharp.IRestClient,RestSharp.Parameter)">
            <summary>
            Add a parameter to use on every request made with this client instance
            </summary>
            <param name="client"><see cref="T:RestSharp.RestClient"/> instance</param>
            <param name="parameter"><see cref="T:RestSharp.Parameter"/> to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultParameter(RestSharp.IRestClient,System.String,System.String)">
            <summary>
            Adds a default HTTP parameter (QueryString for GET, DELETE, OPTIONS and HEAD; Encoded form for POST and PUT)
            Used on every request made by this client instance
            </summary>
            <param name="client"><see cref="T:RestSharp.RestClient"/> instance</param>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultParameter(RestSharp.IRestClient,System.String,System.Object,RestSharp.ParameterType)">
            <summary>
            Adds a default parameter to the client options. There are four types of parameters:
            - GetOrPost: Either a QueryString value or encoded form value based on method
            - HttpHeader: Adds the name/value pair to the HTTP request's Headers collection
            - UrlSegment: Inserted into URL if there is a matching url token e.g. {AccountId}
            - RequestBody: Used by AddBody() (not recommended to use directly)
            Used on every request made by this client instance
            </summary>
            <param name="client"><see cref="T:RestSharp.RestClient"/> instance</param>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <param name="type">The type of parameter to add</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultHeader(RestSharp.IRestClient,System.String,System.String)">
            <summary>
            Adds a default header to the RestClient. Used on every request made by this client instance.
            </summary>
            <param name="client"><see cref="T:RestSharp.RestClient"/> instance</param>
            <param name="name">Name of the header to add</param>
            <param name="value">Value of the header to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultHeaders(RestSharp.IRestClient,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Adds default headers to the RestClient. Used on every request made by this client instance.
            </summary>
            <param name="client"><see cref="T:RestSharp.RestClientOptions"/> instance</param>
            <param name="headers">Dictionary containing the Names and Values of the headers to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultUrlSegment(RestSharp.IRestClient,System.String,System.String)">
            <summary>
            Adds a default URL segment parameter to the RestClient. Used on every request made by this client instance.
            </summary>
            <param name="client"><see cref="T:RestSharp.RestClient"/> instance</param>
            <param name="name">Name of the segment to add</param>
            <param name="value">Value of the segment to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultQueryParameter(RestSharp.IRestClient,System.String,System.String)">
            <summary>
            Adds a default URL query parameter to the RestClient. Used on every request made by this client instance.
            </summary>
            <param name="client"><see cref="T:RestSharp.RestClient"/> instance</param>
            <param name="name">Name of the query parameter to add</param>
            <param name="value">Value of the query parameter to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.Execute(RestSharp.RestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            Executes the request synchronously, authenticating if needed
            </summary>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="cancellationToken">The cancellation token</param>
        </member>
        <member name="M:RestSharp.RestClientExtensions.DownloadStream(RestSharp.RestClient,RestSharp.RestRequest,System.Threading.CancellationToken)">
            <summary>
            A specialized method to download files as streams.
            </summary>
            <param name="client"></param>
            <param name="request">Pre-configured request instance.</param>
            <param name="cancellationToken">The cancellation token</param>
            <returns>The downloaded stream.</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.GetJson``1(RestSharp.RestClient,System.String)">
            <summary>
            Calls the URL specified in the <code>resource</code> parameter, expecting a JSON response back. Deserializes and returns the response.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <typeparam name="TResponse">Response object type</typeparam>
            <returns>Deserialized response object</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.GetJson``1(RestSharp.RestClient,System.String,System.Object)">
            <summary>
            Calls the URL specified in the <code>resource</code> parameter, expecting a JSON response back. Deserializes and returns the response.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <param name="parameters">Parameters to pass to the request</param>
            <typeparam name="TResponse">Response object type</typeparam>
            <returns>Deserialized response object</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.PostJson``2(RestSharp.RestClient,System.String,``0)">
            <summary>
            Serializes the <code>request</code> object to JSON and makes a POST call to the resource specified in the <code>resource</code> parameter.
            Expects a JSON response back, deserializes it to <code>TResponse</code> type and returns it.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <param name="request">Request object, must be serializable to JSON</param>
            <typeparam name="TRequest">Request object type</typeparam>
            <typeparam name="TResponse">Response object type</typeparam>
            <returns>Deserialized response object</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.PostJson``1(RestSharp.RestClient,System.String,``0)">
            <summary>
            Serializes the <code>request</code> object to JSON and makes a POST call to the resource specified in the <code>resource</code> parameter.
            Expects no response back, just the status code.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <param name="request">Request object, must be serializable to JSON</param>
            <typeparam name="TRequest">Request object type</typeparam>
            <returns>Response status code</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.PutJson``2(RestSharp.RestClient,System.String,``0)">
            <summary>
            Serializes the <code>request</code> object to JSON and makes a PUT call to the resource specified in the <code>resource</code> parameter.
            Expects a JSON response back, deserializes it to <code>TResponse</code> type and returns it.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <param name="request">Request object, must be serializable to JSON</param>
            <typeparam name="TRequest">Request object type</typeparam>
            <typeparam name="TResponse">Response object type</typeparam>
            <returns>Deserialized response object</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.PutJson``1(RestSharp.RestClient,System.String,``0)">
            <summary>
            Serializes the <code>request</code> object to JSON and makes a PUT call to the resource specified in the <code>resource</code> parameter.
            Expects no response back, just the status code.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="resource">Resource URL</param>
            <param name="request">Request object, must be serializable to JSON</param>
            <typeparam name="TRequest">Request object type</typeparam>
            <returns>Response status code</returns>
        </member>
        <member name="M:RestSharp.Serializers.Json.RestClientExtensions.UseSystemTextJson(RestSharp.Serializers.SerializerConfig)">
            <summary>
            Use System.Text.Json serializer with default settings
            </summary>
            <param name="serializerConfig"></param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Serializers.Json.RestClientExtensions.UseSystemTextJson(RestSharp.Serializers.SerializerConfig,System.Text.Json.JsonSerializerOptions)">
            <summary>
            Use System.Text.Json serializer with custom settings
            </summary>
            <param name="serializerConfig"></param>
            <param name="options">System.Text.Json serializer options</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Serializers.Json.SystemTextJsonSerializer.#ctor">
            <summary>
            Create the new serializer that uses System.Text.Json.JsonSerializer with default settings
            </summary>
        </member>
        <member name="M:RestSharp.Serializers.Json.SystemTextJsonSerializer.#ctor(System.Text.Json.JsonSerializerOptions)">
            <summary>
            Create the new serializer that uses System.Text.Json.JsonSerializer with custom settings
            </summary>
            <param name="options">Json serializer settings</param>
        </member>
        <member name="M:RestSharp.Serializers.RestSerializers.DeserializeContent``1(RestSharp.RestResponse)">
            <summary>
            Deserialize the response content into the specified type
            </summary>
            <param name="response">Response instance</param>
            <typeparam name="T">Deserialized model type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Serializers.SerializerConfig.UseSerializer(System.Func{RestSharp.Serializers.IRestSerializer})">
            <summary>
            Replace the default serializer with a custom one
            </summary>
            <param name="serializerFactory">Function that returns the serializer instance</param>
        </member>
        <member name="M:RestSharp.Serializers.SerializerConfig.UseSerializer``1">
            <summary>
            Replace the default serializer with a custom one
            </summary>
            <typeparam name="T">The type that implements <see cref="T:RestSharp.Serializers.IRestSerializer"/></typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Serializers.SerializerConfigExtensions.UseJson(RestSharp.Serializers.SerializerConfig)">
            <summary>
            Sets the <see cref="T:RestSharp.Serializers.SerializerConfig"/> to only use JSON
            </summary>
            <param name="config">Configuration instance to work with</param>
            <returns>Reference to the client instance</returns>
        </member>
        <member name="M:RestSharp.Serializers.SerializerConfigExtensions.UseXml(RestSharp.Serializers.SerializerConfig)">
            <summary>
            Sets the <see cref="T:RestSharp.Serializers.SerializerConfig"/> to only use XML
            </summary>
            <param name="config">Configuration instance to work with</param>
            <returns>Reference to the client instance</returns>
        </member>
        <member name="M:RestSharp.Serializers.SerializerConfigExtensions.UseOnlySerializer(RestSharp.Serializers.SerializerConfig,System.Func{RestSharp.Serializers.IRestSerializer})">
            <summary>
            Sets the <see cref="T:RestSharp.Serializers.SerializerConfig"/> to only use the passed in custom serializer
            </summary>
            <param name="config">Configuration instance to work with</param>
            <param name="serializerFactory">Function that returns the serializer instance</param>
            <returns>Reference to the client instance</returns>
        </member>
        <member name="T:RestSharp.Serializers.Xml.DotNetXmlDeserializer">
            <summary>
            Wrapper for System.Xml.Serialization.XmlSerializer.
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.Xml.DotNetXmlDeserializer.Encoding">
            <summary>
            Encoding for serialized content
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.Xml.DotNetXmlDeserializer.RootElement">
            <summary>
            Name of the root element to use when serializing
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.Xml.DotNetXmlDeserializer.Namespace">
            <summary>
            XML namespace to use when serializing
            </summary>
        </member>
        <member name="T:RestSharp.Serializers.Xml.DotNetXmlSerializer">
            <summary>
            Wrapper for System.Xml.Serialization.XmlSerializer.
            </summary>
        </member>
        <member name="M:RestSharp.Serializers.Xml.DotNetXmlSerializer.#ctor">
            <summary>
            Default constructor, does not specify namespace
            </summary>
        </member>
        <member name="M:RestSharp.Serializers.Xml.DotNetXmlSerializer.#ctor(System.String)">
            <inheritdoc />
            <summary>
            Specify the namespaced to be used when serializing
            </summary>
            <param name="namespace">XML namespace</param>
        </member>
        <member name="P:RestSharp.Serializers.Xml.DotNetXmlSerializer.Encoding">
            <summary>
            Encoding for serialized content
            </summary>
        </member>
        <member name="M:RestSharp.Serializers.Xml.DotNetXmlSerializer.Serialize(System.Object)">
            <summary>
            Serialize the object as XML
            </summary>
            <param name="obj">Object to serialize</param>
            <returns>XML as string</returns>
        </member>
        <member name="P:RestSharp.Serializers.Xml.DotNetXmlSerializer.RootElement">
            <summary>
            Name of the root element to use when serializing
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.Xml.DotNetXmlSerializer.Namespace">
            <summary>
            XML namespace to use when serializing
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.Xml.DotNetXmlSerializer.ContentType">
            <summary>
            Content type for serialized content
            </summary>
        </member>
        <member name="M:RestSharp.AsyncHelpers.RunSync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Executes a task synchronously on the calling thread by installing a temporary synchronization context that queues continuations
            </summary>
            <param name="task">Callback for asynchronous task to run</param>
        </member>
        <member name="M:RestSharp.AsyncHelpers.RunSync``1(System.Func{System.Threading.Tasks.Task{``0}})">
            <summary>
            Executes a task synchronously on the calling thread by installing a temporary synchronization context that queues continuations
            </summary>
            <param name="task">Callback for asynchronous task to run</param>
            <typeparam name="T">Return type for the task</typeparam>
            <returns>Return value from the task</returns>
        </member>
        <member name="T:RestSharp.AsyncHelpers.CustomSynchronizationContext">
            <summary>
            Synchronization context that can be "pumped" in order to have it execute continuations posted back to it
            </summary>
        </member>
        <member name="M:RestSharp.AsyncHelpers.CustomSynchronizationContext.#ctor(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Constructor for the custom context
            </summary>
            <param name="task">Task to execute</param>
        </member>
        <member name="M:RestSharp.AsyncHelpers.CustomSynchronizationContext.Post(System.Threading.SendOrPostCallback,System.Object)">
            <summary>
            When overridden in a derived class, dispatches an asynchronous message to a synchronization context.
            </summary>
            <param name="function">Callback function</param>
            <param name="state">Callback state</param>
        </member>
        <member name="M:RestSharp.AsyncHelpers.CustomSynchronizationContext.Run">
            <summary>
            Enqueues the function to be executed and executes all resulting continuations until it is completely done
            </summary>
        </member>
        <member name="M:RestSharp.AsyncHelpers.CustomSynchronizationContext.Send(System.Threading.SendOrPostCallback,System.Object)">
            <summary>
            When overridden in a derived class, dispatches a synchronous message to a synchronization context.
            </summary>
            <param name="function">Callback function</param>
            <param name="state">Callback state</param>
        </member>
        <member name="M:RestSharp.AsyncHelpers.CustomSynchronizationContext.CreateCopy">
            <summary>
            When overridden in a derived class, creates a copy of the synchronization context. Not needed, so just return ourselves.
            </summary>
            <returns>Copy of the context</returns>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.ExecuteGet``1(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Executes a GET-style request synchronously, authenticating if needed.
            The response content then gets deserialized to T.
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <returns>Deserialized response content</returns>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.ExecuteGet(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Executes a GET-style synchronously, authenticating if needed
            </summary>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.ExecutePost``1(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Executes a POST-style request synchronously, authenticating if needed.
            The response content then gets deserialized to T.
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <returns>Deserialized response content</returns>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.ExecutePost(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Executes a POST-style synchronously, authenticating if needed
            </summary>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.ExecutePut``1(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Executes a PUT-style request synchronously, authenticating if needed.
            The response content then gets deserialized to T.
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <returns>Deserialized response content</returns>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.ExecutePut(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Executes a PUP-style synchronously, authenticating if needed
            </summary>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.Execute``1(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Executes the request synchronously, authenticating if needed
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.Execute(RestSharp.RestClient,RestSharp.RestRequest,RestSharp.Method)">
            <summary>
            Executes the request synchronously, authenticating if needed
            </summary>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="httpMethod">Override the request method</param>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.Execute``1(RestSharp.RestClient,RestSharp.RestRequest,RestSharp.Method)">
            <summary>
            Executes the request synchronously, authenticating if needed
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="client"></param>
            <param name="request">Request to be executed</param>
            <param name="httpMethod">Override the request method</param>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.Get``1(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Execute the request using GET HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.Post``1(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Execute the request using POST HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.Put``1(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Execute the request using PUT HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.Head``1(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Execute the request using HEAD HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.Options``1(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Execute the request using OPTIONS HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.Patch``1(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Execute the request using PATCH HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.Delete``1(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            Execute the request using DELETE HTTP method. Exception will be thrown if the request does not succeed.
            The response data is deserialized to the Data property of the returned response object.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">The request</param>
            <typeparam name="T">Expected result type</typeparam>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientSyncExtensions.DownloadData(RestSharp.RestClient,RestSharp.RestRequest)">
            <summary>
            A specialized method to download files.
            </summary>
            <param name="client">RestClient instance</param>
            <param name="request">Pre-configured request instance.</param>
            <returns>The downloaded file.</returns>
        </member>
    </members>
</doc>
