<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
	<UsingTask TaskName="Xamarin.Forms.Build.Tasks.XamlGTask" AssemblyFile="Xamarin.Forms.Build.Tasks.dll"/>
	<UsingTask TaskName="Xamarin.Forms.Build.Tasks.FixedCreateCSharpManifestResourceName" AssemblyFile="Xamarin.Forms.Build.Tasks.dll"/>
	<UsingTask TaskName="Xamarin.Forms.Build.Tasks.XamlCTask" AssemblyFile="Xamarin.Forms.Build.Tasks.dll"/>

	<PropertyGroup>
		<EnableDefaultXamlItems Condition="'$(EnableDefaultXamlItems)'==''">True</EnableDefaultXamlItems>
		<_DefaultXamlItemsEnabled>False</_DefaultXamlItemsEnabled>
	</PropertyGroup>

	<Import Project="$(MSBuildThisFileDirectory)Xamarin.Forms.DefaultItems.targets" Condition="'$(MSBuildSDKsPath)'!=''" />

	<PropertyGroup>
		<CoreCompileDependsOn>
			XamlG;
			$(CoreCompileDependsOn);
		</CoreCompileDependsOn>
	</PropertyGroup>
  
	<PropertyGroup>
		<CompileDependsOn>
			$(CompileDependsOn);
			XamlC;
		</CompileDependsOn>
	</PropertyGroup>
  
	<Target Name="UpdateDesignTimeXaml" DependsOnTargets="XamlG"/>
  
	<Target Name="XamlG" DependsOnTargets="$(XamlGDependsOn)"/>
  
	<PropertyGroup>
		<XamlGDependsOn>
			_PreXamlG;
			_CollectXamlFiles;
			_CoreXamlG;
		</XamlGDependsOn>
	</PropertyGroup>
  
	<Target Name="_PreXamlG">
		<MakeDir Directories="$(IntermediateOutputPath)"/>
	</Target>
  
	<Target Name="_CollectXamlFiles">
		<ItemGroup>
			<_XamlResources Include="@(EmbeddedResource)" Condition="'%(Extension)' == '.xaml' AND '$(DefaultLanguageSourceExtension)' == '.cs'"/>
		</ItemGroup>
		<FixedCreateCSharpManifestResourceName ResourceFiles="@(_XamlResources)" RootNamespace="$(RootNamespace)">
			<Output TaskParameter="ResourceFilesWithManifestResourceNames" ItemName="XamlFiles" />
		</FixedCreateCSharpManifestResourceName>
		<ItemGroup>
			<XamlGFiles Include="@(XamlFiles->'$(IntermediateOutputPath)%(ManifestResourceName).g$(DefaultLanguageSourceExtension)')"/>
			<Compile Include="@(XamlGFiles)"/>
			<FileWrites Include="@(XamlGFiles)"/>
		</ItemGroup>
	</Target>
  
	<Target Name="_CoreXamlG"
		Inputs = "@(XamlFiles)"
		Outputs = "$(IntermediateOutputPath)%(ManifestResourceName).g$(DefaultLanguageSourceExtension)">
		<XamlGTask
			Source="@(XamlFiles)"
			Language = "$(Language)"
			AssemblyName = "$(AssemblyName)"
			OutputFile = "$(IntermediateOutputPath)%(ManifestResourceName).g$(DefaultLanguageSourceExtension)">
		</XamlGTask>
	</Target>
  
	<Target Name="XamlC">
		<XamlCTask
			Assembly = "$(IntermediateOutputPath)$(TargetFileName)"
			ReferencePath = "@(ReferencePath)"
			Verbosity = "2"
			OptimizeIL = "true"
			DebugSymbols = "$(DebugSymbols)"
			DebugType = "$(DebugType)"/>
	</Target>
</Project>
