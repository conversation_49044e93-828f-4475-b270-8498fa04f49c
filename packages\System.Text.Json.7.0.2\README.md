## About

Provides high-performance and low-allocating types that serialize objects to JavaScript Object Notation (JSON) text and deserialize JSON text to objects, with UTF-8 support built-in. Also provides types to read and write JSON text encoded as UTF-8, and to create an in-memory document object model (DOM), that is read-only, for random access of the JSON elements within a structured view of the data.

The `System.Text.Json` library is built-in as part of the shared framework in .NET Runtime. The package can be installed when you need to use it in other target frameworks.

For more information, see the documentation:

- [JSON serialization and deserialization in .NET](https://docs.microsoft.com/dotnet/standard/serialization/system-text-json-overview)
- [How to serialize and deserialize JSON in .NET](https://docs.microsoft.com/dotnet/standard/serialization/system-text-json-how-to)
- [System.Text.Json API reference](https://docs.microsoft.com/dotnet/api/system.text.json)
