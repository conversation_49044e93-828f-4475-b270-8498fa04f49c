﻿<?xml version="1.0" encoding="utf-8"?>
<Roots xmlns="clr-namespace:Microsoft.Xaml.Tools.XamlCompiler.RootLog;assembly=Microsoft.Windows.UI.Xaml.81.Build.Tasks">
  <Roots.RootTypes>
    <RootType FullName="Windows.UI.Xaml.Controls.ContentControl">
      <RootProperty Name="HorizontalContentAlignment" />
      <RootProperty Name="VerticalContentAlignment" />
      <RootProperty Name="Content" />
    </RootType>
    <RootType FullName="Xamarin.Forms.Platform.WinRT.PageControl" />
    <RootType FullName="Windows.UI.Xaml.Controls.UserControl">
      <RootProperty Name="Content" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.Grid">
      <RootProperty Name="Resources" />
      <RootProperty Name="ColumnDefinitions" />
      <RootProperty Name="Children" />
      <RootMethod Name="GetColumn" />
      <RootMethod Name="SetColumn" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.Button">
      <RootProperty Name="Name" />
      <RootProperty Name="Content" />
      <RootProperty Name="BorderThickness" />
      <RootEvent Name="Click" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.FrameworkElement">
      <RootProperty Name="Margin" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.Control">
      <RootProperty Name="Template" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Setter">
      <RootProperty Name="Property" />
      <RootProperty Name="Value" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ControlTemplate">
      <RootProperty Name="TargetType" />
      <RootProperty Name="Template" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.VisualStateManager">
      <RootMethod Name="GetVisualStateGroups" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.VisualStateGroup">
      <RootProperty Name="States" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.VisualState">
      <RootProperty Name="Storyboard" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.Storyboard">
      <RootProperty Name="Children" />
      <RootMethod Name="GetTargetProperty" />
      <RootMethod Name="SetTargetProperty" />
      <RootMethod Name="GetTargetName" />
      <RootMethod Name="SetTargetName" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.DoubleAnimation">
      <RootProperty Name="Duration" />
      <RootProperty Name="To" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.ObjectAnimationUsingKeyFrames">
      <RootProperty Name="KeyFrames" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Media.Animation.DiscreteObjectKeyFrame">
      <RootProperty Name="KeyTime" />
      <RootProperty Name="Value" />
    </RootType>
    <RootType FullName="Microsoft.Xaml.Tools.DirectUI.ProxyTypes.ThemeResourceExtension" />
    <RootType FullName="Windows.UI.Xaml.Controls.Border">
      <RootProperty Name="BorderBrush" />
      <RootProperty Name="BorderThickness" />
      <RootProperty Name="Background" />
      <RootProperty Name="Margin" />
      <RootProperty Name="Child" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Shapes.Rectangle">
      <RootProperty Name="IsHitTestVisible" />
      <RootProperty Name="Opacity" />
      <RootProperty Name="StrokeDashOffset" />
      <RootProperty Name="StrokeEndLineCap" />
      <RootProperty Name="Stroke" />
      <RootProperty Name="StrokeDashArray" />
    </RootType>
    <RootType FullName="Microsoft.Xaml.Tools.DirectUI.ProxyTypes.TemplateBindingExtension" />
    <RootType FullName="Windows.UI.Xaml.Controls.ContentPresenter">
      <RootProperty Name="ContentTemplate" />
      <RootProperty Name="ContentTransitions" />
      <RootProperty Name="Content" />
      <RootProperty Name="HorizontalAlignment" />
      <RootProperty Name="Margin" />
      <RootProperty Name="VerticalAlignment" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Automation.AutomationProperties">
      <RootMethod Name="GetAccessibilityView" />
      <RootMethod Name="SetAccessibilityView" />
    </RootType>
    <RootType FullName="Windows.UI.Xaml.Controls.ColumnDefinition">
      <RootProperty Name="Width" />
    </RootType>
    <RootType FullName="Xamarin.Forms.Platform.WinRT.StepperControl" />
  </Roots.RootTypes>
</Roots>
