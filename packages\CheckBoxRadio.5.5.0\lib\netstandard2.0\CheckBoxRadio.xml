<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CheckBoxRadio</name>
    </assembly>
    <members>
        <member name="T:LaavorCheckBoxRadio.CheckBox">
            <summary>
            Class CheckBox
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.IsChecked">
            <summary>
            Inform checkbox state 
            </summary>
        </member>
        <member name="E:LaavorCheckBoxRadio.CheckBox.Checked">
            <summary>
            Event call when checkbox is checked or uncked
            </summary>
        </member>
        <member name="M:LaavorCheckBoxRadio.CheckBox.#ctor">
            <summary>
            Constructor of ChekBox
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.CheckBox.IsReadOnlyProperty">
            <summary>
            Property to report if Check<PERSON><PERSON> is readonly.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.IsReadOnly">
            <summary>
            Property to report if CheckB<PERSON> is readonly.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.CheckBox.InitialStateProperty">
            <summary>
            Property inform initial state of checkbox.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.InitialState">
            <summary>
            Property inform initial state of checkbox.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.ColorUI">
            <summary>
            Set if is ColorUI
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.TouchType">
            <summary>
            Set if is TouchType
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.DesignType">
            <summary>
            Set DesignType
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.CheckBox.HeightProperty">
            <summary>
            Enter the height checkbox.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.Height">
            <summary>
            Enter the height checkbox.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.CheckBox.WidthProperty">
            <summary>
            Enter the width checkbox.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.Width">
            <summary>
            Enter the width checkbox.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.CheckBox.TextProperty">
            <summary>
            Enter the text
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.Text">
            <summary>
            Enter the text
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.CheckBox.TextCheckedProperty">
            <summary>
            Enter the text default -- (Is Optional  you can add your Label)
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.TextChecked">
            <summary>
            Enter the TextChecked -- (Is Optional)
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.CheckBox.FontSizeProperty">
            <summary>
            Enter the font size of text.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.FontSize">
            <summary>
            Enter the font size of text.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.TextColor">
            <summary>
            Set TextColor
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.CheckBox.FontFamilyProperty">
            <summary>
            Enter the font family.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.FontFamily">
            <summary>
            Enter the font family.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.Vivacity">
            <summary>
            Set Vivacity
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.VivacitySpeed">
            <summary>
            VivacitySpeed changes animation speed when selecting an item. 
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBox.Depth">
            <summary>
            Set Vivacity
            </summary>
        </member>
        <member name="T:LaavorCheckBoxRadio.CheckBoxImage">
            <summary>
            Class CheckBoxImage
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBoxImage.Label">
            <summary>
            Internal
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBoxImage.Value">
            <summary>
            Value
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.CheckBoxImage.IsChecked">
            <summary>
            Inform if is Checked
            </summary>
        </member>
        <member name="M:LaavorCheckBoxRadio.CheckBoxImage.#ctor(System.String,LaavorCheckBoxRadio.DesignType,LaavorCheckBoxRadio.ColorUI,System.Boolean)">
            <summary>
            Constructor of CheckBoxImage
            </summary>
            <param name="hash"></param>
            <param name="color"></param>
            <param name="designType"></param>
            <param name="isChecked"></param>
        </member>
        <member name="M:LaavorCheckBoxRadio.CheckBoxImage.setlabel(System.String,LaavorCheckBoxRadio.LabelCheck)">
            <summary>
            Internal
            </summary>
            <param name="hash"></param>
            <param name="label"></param>
        </member>
        <member name="M:LaavorCheckBoxRadio.CheckBoxImage.ChangeState(System.String,System.Boolean)">
            <summary>
            Change State Internal
            </summary>
            <param name="hash"></param>
            <param name="isChecked"></param>
        </member>
        <member name="M:LaavorCheckBoxRadio.CheckBoxImage.ChangeColor(System.String,LaavorCheckBoxRadio.ColorUI)">
            <summary>
            Change State Internal
            </summary>
            <param name="hash"></param>
            <param name="color"></param>
        </member>
        <member name="M:LaavorCheckBoxRadio.CheckBoxImage.ChangeDesignType(System.String,LaavorCheckBoxRadio.DesignType)">
            <summary>
            Change State Internal
            </summary>
            <param name="hash"></param>
            <param name="designType"></param>
        </member>
        <member name="T:LaavorCheckBoxRadio.ColorUI">
            <summary>
            Colors UI Laavor
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Black">
            <summary>
            Black
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Blue">
            <summary>
            Blue
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Gray">
            <summary>
            Gray
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Green">
            <summary>
            Green
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Red">
            <summary>
            Red
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Yellow">
            <summary>
            Yellow
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Orange">
            <summary>
            Orange
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Pink">
            <summary>
            Pink
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.White">
            <summary>
            White
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.BlueLight">
            <summary>
            BlueLight
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.YellowLight">
            <summary>
            YellowLight
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.GreenLight">
            <summary>
            GreenLight
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Brown">
            <summary>
            Brown
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Purple">
            <summary>
            Purple 
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Turquoise">
            <summary>
            Turquoise 
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.PinkLight">
            <summary>
            PinkLight 
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.BlueSky">
            <summary>
            BlueSky
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.GrayLight">
            <summary>
            GrayLight
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.RedLight">
            <summary>
            RedLight
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.OrangeLight">
            <summary>
            OrangeLight
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.YellowDark">
            <summary>
            YellowDark
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.GreenDark">
            <summary>
            GreenDark
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.BlueDark">
            <summary>
            BlueDark
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Aqua">
            <summary>
            Aqua
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.Tan">
            <summary>
            Tan
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.GreenDarkness">
            <summary>
            GreenDarkness
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.ColorUI.BlueViolet">
            <summary>
            BlueViolet
            </summary>
        </member>
        <member name="T:LaavorCheckBoxRadio.Colors">
            <summary>
            Class Colors
            </summary>
        </member>
        <member name="M:LaavorCheckBoxRadio.Colors.Get(LaavorCheckBoxRadio.ColorUI)">
            <summary>
            Get Method
            </summary>
            <param name="color"></param>
            <returns></returns>
        </member>
        <member name="T:LaavorCheckBoxRadio.Vivacity">
            <summary>
            Vivacity
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.Vivacity.Increase">
            <summary>
            Increase
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.Vivacity.Decrease">
            <summary>
            Decrease
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.Vivacity.Jump">
            <summary>
            Jump
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.Vivacity.Rotation">
            <summary>
            Rotation
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.Vivacity.Down">
            <summary>
            Down
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.Vivacity.Left">
            <summary>
            Left
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.Vivacity.None">
            <summary>
            None
            </summary>
        </member>
        <member name="T:LaavorCheckBoxRadio.Depth">
            <summary>
            Depth
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.Depth.Small">
            <summary>
            Small
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.Depth.LessMedium">
            <summary>
            LessMedium
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.Depth.Medium">
            <summary>
            Medium
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.Depth.Large">
            <summary>
            Large
            </summary>
        </member>
        <member name="T:LaavorCheckBoxRadio.DesignType">
            <summary>
            DesignType
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.Standard">
            <summary>
            Starndard 
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.Shinning">
            <summary>
            Shinning
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.Fit">
            <summary>
            Fit
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.Filled">
            <summary>
            Filled
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.Allonsy">
            <summary>
            Allonsy
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.Scratches">
            <summary>
            Scratches
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.CurvedEdge">
            <summary>
            CurvedEdge
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.Cadre">
            <summary>
            Cadre
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.Smoke">
            <summary>
            Smoke
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.StampFilled">
            <summary>
            StampFilled
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.StampFit">
            <summary>
            StampFit
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.DesignType.Tile">
            <summary>
            Tile
            </summary>
        </member>
        <member name="T:LaavorCheckBoxRadio.VivacitySpeed">
            <summary>
            VivacitySpeed 
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.VivacitySpeed.Slow">
            <summary>
            Slow
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.VivacitySpeed.Normal">
            <summary>
            Normal
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.VivacitySpeed.Fast">
            <summary>
            Fast
            </summary>
        </member>
        <member name="T:LaavorCheckBoxRadio.TouchType">
            <summary>
            TouchType
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.TouchType.Unique">
            <summary>
            Unique
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.TouchType.WithText">
            <summary>
            WithText
            </summary>
        </member>
        <member name="T:LaavorCheckBoxRadio.LabelCheck">
            <summary>
            Class LabelCheck
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.LabelCheck.CheckBoxImage">
            <summary>
            Internal
            </summary>
        </member>
        <member name="M:LaavorCheckBoxRadio.LabelCheck.setImage(System.String,LaavorCheckBoxRadio.CheckBoxImage)">
            <summary>
            Internal
            </summary>
            <param name="hash"></param>
            <param name="image"></param>
        </member>
        <member name="T:LaavorCheckBoxRadio.LabelRadio">
            <summary>
            Class Label Radio
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.LabelRadio.Image">
            <summary>
            Internal
            </summary>
        </member>
        <member name="M:LaavorCheckBoxRadio.LabelRadio.setImage(System.String,LaavorCheckBoxRadio.RadioImage)">
            <summary>
            Internal
            </summary>
            <param name="hash"></param>
            <param name="radioImage"></param>
        </member>
        <member name="T:LaavorCheckBoxRadio.RadioBox">
            <summary>
            Class RadioBox
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioBox.RadioImage">
            <summary>
            Internal
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioBox.LabelRadio">
            <summary>
            Internal
            </summary>
        </member>
        <member name="T:LaavorCheckBoxRadio.RadioButton">
            <summary>
            Class RadioButton
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.ValueSelected">
            <summary>
            Value of item selected
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.TextSelected">
            <summary>
            Text of item selected
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.IndexSelected">
            <summary>
            Index of item selected
            </summary>
        </member>
        <member name="E:LaavorCheckBoxRadio.RadioButton.OnChangeSelected">
            <summary>
            Event call When Selected Item is changed
            </summary>
        </member>
        <member name="M:LaavorCheckBoxRadio.RadioButton.#ctor">
            <summary>
            Constructor of RadioButton
            </summary>
        </member>
        <member name="M:LaavorCheckBoxRadio.RadioButton.ResetAll">
            <summary>
            Clear the list Images - necessary insert ChoiceImageSwapItem again
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.IsReadOnlyProperty">
            <summary>
            Property to report if RadioButton is readonly.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.IsReadOnly">
            <summary>
            Property to report if RadioButton is readonly.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.InitialIndexProperty">
            <summary>
            Property inform initial index selected of RadioButton.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.InitialIndex">
            <summary>
            Property inform initial index selected of RadioButton.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.InitialValueProperty">
            <summary>
            Property inform initial value selected of RadioButton.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.InitialValue">
            <summary>
            Property inform initial value selected of RadioButton.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.ColorUI">
            <summary>
            Set if is ColorUI
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.TouchType">
            <summary>
            Set if is TouchType
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.DesignType">
            <summary>
            Set DesignType
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.HeightItemProperty">
            <summary>
            Enter the height, represents only one radioitem.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.HeightItem">
            <summary>
            Enter the height, represents only one radioitem.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.WidthItemProperty">
            <summary>
            Enter the width, represents only one radioitem.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.WidthItem">
            <summary>
            Enter the width, represents only one radioitem.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.FontSizeProperty">
            <summary>
            Enter the font size of text.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.FontSize">
            <summary>
            Enter the font size of text.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.TextColor">
            <summary>
            Set TextColor
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.ListItemsProperty">
            <summary>
            Enter ListItems (Data items).
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.ListItems">
            <summary>
            Enter ListItems (Data items).
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.ValueFieldProperty">
            <summary>
            Enter the ValueField, only when using list.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.ValueField">
            <summary>
            Enter the ValueField, only when using list.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.TextFieldProperty">
            <summary>
            Enter the TextField, only when using list.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.TextField">
            <summary>
            Enter the TextField, only when using list.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.RowFieldProperty">
            <summary>
            Enter the RowField, only when using list.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.RowField">
            <summary>
            Enter the RowField, only when using list.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.ColFieldProperty">
            <summary>
            Enter the ColField, only when using list.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.ColField">
            <summary>
            Enter the ColField, only when using list.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioButton.FontFamilyProperty">
            <summary>
            Enter the font family.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.FontFamily">
            <summary>
            Enter the font family.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.VivacitySpeed">
            <summary>
            VivacitySpeed changes animation speed when selecting an item. 
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.Vivacity">
            <summary>
            Set Vivacity
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioButton.Depth">
            <summary>
            Set Vivacity
            </summary>
        </member>
        <member name="M:LaavorCheckBoxRadio.RadioButton.OnChildAdded(Xamarin.Forms.Element)">
            <summary>
            Internal.
            </summary>
        </member>
        <member name="T:LaavorCheckBoxRadio.RadioImage">
            <summary>
            Class RadioImage
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioImage.Index">
            <summary>
            Index
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioImage.Value">
            <summary>
            Value
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioImage.IsSelected">
            <summary>
            Inform if is Selected
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioImage.Label">
            <summary>
            Internal
            </summary>
        </member>
        <member name="M:LaavorCheckBoxRadio.RadioImage.#ctor(System.String,LaavorCheckBoxRadio.DesignType,LaavorCheckBoxRadio.ColorUI,System.Boolean,System.Int32,System.String)">
            <summary>
            Constructor of RadioImage
            </summary>
            <param name="hash"></param>
            <param name="color"></param>
            <param name="designType"></param>
            <param name="selected"></param>
            <param name="index"></param>
            <param name="value"></param>
        </member>
        <member name="M:LaavorCheckBoxRadio.RadioImage.setLabel(System.String,LaavorCheckBoxRadio.LabelRadio)">
            <summary>
            Internal
            </summary>
            <param name="hash"></param>
            <param name="labelRadio"></param>
        </member>
        <member name="M:LaavorCheckBoxRadio.RadioImage.ChangeState(System.String,System.Boolean)">
            <summary>
            Class ChangeState
            </summary>
            <param name="hash"></param>
            <param name="selected"></param>
        </member>
        <member name="M:LaavorCheckBoxRadio.RadioImage.ChangeColor(System.String,LaavorCheckBoxRadio.ColorUI)">
            <summary>
            ChangeColor
            </summary>
            <param name="hash"></param>
            <param name="color"></param>
        </member>
        <member name="M:LaavorCheckBoxRadio.RadioImage.ChangeDesignType(System.String,LaavorCheckBoxRadio.DesignType)">
            <summary>
            ChangeDesignType
            </summary>
            <param name="hash"></param>
            <param name="designType"></param>
        </member>
        <member name="T:LaavorCheckBoxRadio.RadioItem">
            <summary>
            Class RadioItem
            </summary>
        </member>
        <member name="M:LaavorCheckBoxRadio.RadioItem.#ctor">
            <summary>
            Constructor of RadioItem
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioItem.HeightProperty">
            <summary>
            Enter the height, represents only item.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioItem.Height">
            <summary>
            Enter the height, represents only item.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioItem.WidthProperty">
            <summary>
            Enter the width, represents only one item.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioItem.Width">
            <summary>
            Enter the width, represents only one item.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioItem.TextProperty">
            <summary>
            Enter the text
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioItem.Text">
            <summary>
            Enter the text
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioItem.ValueProperty">
            <summary>
            Enter the Value
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioItem.Value">
            <summary>
            Enter the value
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioItem.FontSizeProperty">
            <summary>
            Enter the font size of text.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioItem.FontSize">
            <summary>
            Enter the font size of text.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioItem.TextColorProperty">
            <summary>
            Enter the text color.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioItem.TextColor">
            <summary>
            Enter the text color.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioItem.FontFamilyProperty">
            <summary>
            Enter the font family.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioItem.FontFamily">
            <summary>
            Enter the font family.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioItem.IsCheckedProperty">
            <summary>
            Enter the IsChecked.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioItem.IsChecked">
            <summary>
            Enter the IsChecked.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioItem.RowProperty">
            <summary>
            Enter the Row.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioItem.Row">
            <summary>
            Enter the Row.
            </summary>
        </member>
        <member name="F:LaavorCheckBoxRadio.RadioItem.ColProperty">
            <summary>
            Enter the Col.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.RadioItem.Col">
            <summary>
            Enter the Col.
            </summary>
        </member>
        <member name="T:LaavorCheckBoxRadio.Tile">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Aqua">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAtUSURBVHic7Z1fjF1FHce/v9+ce/furo2ttN0IAo2QEpJGYkiUByUhUsID4YW0URNBrAQSNKJGH9QQ4cGADyYaYkMj+CDEsCJRY59INPAAMSQaKy0SpJUUMaW225bu7d5zfn98OOfcPXt777Llz166M59kcmd+Z2bOzDnfnpk9vblfgjsB2AXgWwCuAMBIrGUMwD8B/ATAowT3OwDsGe+YEmPiDoL7AQBXAvjhvbOztH5h4W64ZyASuCuICgBiZV6YSBwQAOruAiJxdyEiNUCISOr6xFygqlsfM3dxQJm57IdI1Ez7/TArmMXchUIoVFXBLHUqGuVCVZ1ZEIJ4CJKrqocgFoJICAVNTOgCINpuy2kR [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.AquaSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABjfSURBVHic7Z17tCRVfe8/e+/q7vOYxznzZsIoIAwaQATirCRoEgIkuWRd11UDcqMYUYyYKCqiMQrJ+EbDzb3eJF7Em6CXREnwEUnQxOglBHkj0cwwI0tmeA0zzAzM6zz6dNXe+5c/dlWf6j6POedU95xXf9bq1bWrdv+6qvrXe+/aj99XASByOfB+4KWAocNCxgHbgRtQ6ssKkSuAL87ySXWYHd6qENkGvAz42LW33mpX1GrvBkooZRFxKJUA1odtq5WyAhZwImJRyoqIVUo5D1YpZbP8SuuENG92zItYAae1DnaUss57V7ejtUNr60WsMiZxzjm0ttkryaUT55xobTHGijE2ds6JMdYbY60xiapU [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Black">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAwZSURBVHic7Z3NixzHGcaf962e0e7Km2ziXdlRYJ3gRMYYmwRBLEJi7MQHY3IwGIn4YAXbCTbIhwRECCYIO7nIh0AOAllyiC65WDr5EJOT/4UEZSVBIMJG+bBli10vu7PT9fXmMFW9NbMz69lY0liq+kHTPfUxW1X9qOudalEPABCAFwBcAGABSDlu68OGe/08ws3/GYDTKOTITwnARQD3A3jt2LFjmJubOwKgQk8pzeG9dwAsM1sRsQBcOFsRsUTkvPeWiJryRGRC/SbPe29FxCXfY51zLv2eUN8SkYl58TDGuPQ6bYPW2omI9d5ba60hItftdq1zzq6trTmttf3kk0/syspKvbS0pOMo7N+/f+rg [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.BlackSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAInQAACJ0Bso74sQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7X17kBXF2fevey7n7H1ZWK7LLbCCXLzjjRf9gm+0ol/C+4pGE42KYrTKSlIxhuIrU5WqVIyXlElFAojEiGIw3i+IRkURgaCCggIiyn3Z+y57PXvOmenu5/tjpg/DYXfZPWcXduH8qqZmpqenz/SZp7ufebqf58cAWABGADgXwBkAcgEwZHAqggC0AvgGwBYA5Sa8lz8HwPcB5ABQAMA88OQCiKi3Hiz1mzt+pl552H4ODiAC4G0AT5nwWv73ARQA+M+ECROGDB8+/GzbtguJSAFQ5EHpzU9r9xyAUkodlZ583t41vSWfBzcpJSXllYH8FLxXSpkoSymVOJdSCsdxBJKEY8CAAYVjx449 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Blue">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA1NSURBVHic7Z1NiCbHecf/z1P99s6HNhkr++HIycbBZIVxbBIEsQ6JkZI4CJODwewSH7xBso0E60McRA7GLHJykQ+GHCwWSYl1SQ5ehYADMYGADz6EQC5BXmlJwBsLJY61q2VWk5l3prueDx+q6317Zmd2Z3F2Z7VVPyi6urqqp6r7P/3UdA/1J8AJwFMA/hTAwwACKvczCuA/AHwDoG8R4F8E8NIBd6pyMHyBAH8DwIcBfO3cuQtYWdk6C3gDkACYJTNXgISZxB0CQN1dABJ3FyJSMwgRzeoTcRzaz46ZubhDmXk4D4mq6fw8rACLmQtRiKqqAEtOMc73Y1R1ZwGCuAfpe1X3IGZBREIkOqRbWxDV [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.BlueDark">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA2zSURBVHic7Z1dqGbXXcaf57/2+87JmYyZfJzEREiapCbE1qK0tlW0TZBAFRHBzqCiIyWUBEaRKBb8IMRe2QsFIRgySLzxphPxA1S86qXghVRiPoTopDVNazOmZ5qZOXPevf4fXqy997znnTmZGUrmZGatH2z23utjn7XW+5y9/me9h/UQAHHvZx+D8LcReBCAoHE94wD+E4E/xesvPE/ce/jzYBzb61Y19oCIz3eQeBIBAPH0U5/7BA8eWD8KRAdQgTAAGaB6uAFUITUiFICVc7knaR6hJBSguruRzAAUQMkD1D00ABMRDQ8FoWZuAKbnDPWVItnUDKSW51CzqpW2QXNWi+E6QO373iKoHlA1z2Sy [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.BlueDarkSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAB2pSURBVHic7Z15mFxF1f8/p273bEnIRsjCEpNAFsMmAgoKgsBPRUQxGJBFBeMLIiRAIKIsQgQEN0RkjRAQULYXFGURURREFuVlMSQoBEgCISF7ZqZ7+t6qOr8/bt+enslMMpk7k8wk/X2efrpP3apz69Y9XbduVZ3zFQBGH30SyHRgPBBQwZYMB8xD5MfMv+c2YdTRUxCZtblrVcHmgJ6cwcjZKAAzLzhpXzuoX+0ZQBbEgjogArFevQOxRsSqqgVc/B3LIuK8qhXBgljvvQOsiERAfIz4mPPeGWOserUI1rk4b6KnWN6KMZGzziFiKZaNrHVx3bBRZJ0WfytiwzB0qmK9YsPI2iDI2qYwss6LbWjI [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.BlueLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA5eSURBVHic7Z1PrB1HVsa/c6r7Pvu9PBJP5BgYUEYokwEJjRhBEAuEEoEQC5Y4/FkYoTBKpAxCQYINaBTNjgVILEIUC7KCRZwRArFBYjcbkDIjpJlJiAQTJgIyCtbMS+y853e7zjkfi6q+vu/az9iE+A2u+knt7q6ualfV/dx1utqqT0jKF7+Cpwj8DoBPAVB07mUCwJsg/vj8T+BleeUr/KwQF0+6Vp27jwCfHYR4DgBE8Px7X78ktMNnAQ6AGEAHJENgDDpETFWMhAFwkAYRI2ki4gAMEBOBRdBVNUNKXhImNS8BT6o2p3mEz9dU1UXUgrSUUnZ3F1UTKZuHu4iaqpr5fJxMU7Js7vMxNOVh3PIg [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.BlueLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABrJSURBVHic7Z1pkCTHdd//L7O6p6dnZ3dm7wOLxUEsjgVAkYQoWwyFySD5RR8UYcsk7QjRNmk6RMs26JBshQ9F2GHJEZbDCtph02aAQYE0LYsm+EGCpSDDpkTrAAWAi2MBLhYHF7uLvQ/sMVdPV718f3/Iqr52Z3dmqmd2jv5FdHRnHVlVWa8rs17my78AwFMv8DMkfgXAAwA8BqxlAoAjBP7Dpx6Tr8tTB/k5Al+53Wc1YPkRwWcTAr+cp//NlVe/qWLNfwSgAogCDIBkECiNASLqnCgJBRBAKkSUpIpIAKCAqAjUjEFEVJxkAAIJFREFoEYG75wWy4JZkDwf51wQcWqkeu+zEEIQ51QkfoKFIOLU [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.BlueSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABosSURBVHic7Z15mB1Vnfc/51Td20vSSXf2sJNAMJMAssVXgeeVgTyOmWd4BpDASEDAOCwvBIiBRyDRoIAgOgwjOgjjRGVRBBlhQEaEyQgiw6IOkAWGxCyEbJ29l9u36izvH6fqbr2ku+t2p5f7eZ773DqnTv1uLb9bdeosv68AAHsZsAg4BvCoMJTRwHvAPSB+LMDOBx46wDtV4cBwuQC7CpgOfH3x4p+pMWOy1wIpEAqsBhECyhirQSgphbIWBWhrrQKhrLVKCKGNQQkhVFxeCBmCKxuvM8Yqa9FSysiOUFobnbcjNUhljFVCeKHWWoNU8ScM8+kw1NpaqcBT1noqCLS21lPGeEopLxSiSre1obRO [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.BlueSky">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA87SURBVHic7Z1PrGVJXce/31+de9/r1zMCw8x0AEXEODLGEI1iXEgciDEu3PZENhhBZWKzEGJcEYK6cUPiBsceFDe6oIeQ4MqNGxZG44bwz2iCxGAg2NP0OMN73ffU7/f7uqhz7rvvdb+ZaZPph3Pqk1ROnfpzblW9b5+qW7dTX0Li5av4AIiPAvhJAAWd1zIB4N9IfPLa7/IzfPIZ/Y6EZ867VZ1zQPjtQcJHAIDAH70rr+HAbl8RNBB0AC7ACXhKQdBpdAkOICQ5SZ+uIcFBHpenVRAOYZsnyQGEmfmclpEx59EsCPOU3KzUyAjSHDQnzd2ne9uJszit+FgjyOKw4olSy7AXNeC0tR9tPGT7fqvS [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.BlueSkySelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABv7SURBVHic7Z15lB3Vfec/v1v1Xi/aJYSEWIVYzY5BTExiY2Nm8diesY13ezL2eMYkmcE+cZIzM8k5syRzzmTO5DiZ2A7GYwPBTjAwXrAdHBuMbXYMxgKEICBALGLRitTbq7r395s/btV7r1tqtbrrdau79T7n9Om6Vbd+79Z9v1d16y6/rwBcfpV9QoTPAacBCV3mMwHYJPC/b/y0XCeXf9k+JfCVQ12qLjOPGZ9MBX43pvjv6+0G3+ca/wGoCeINCyC5gFezIIgXJ94MDwQz8yLii//BDI+Ib+YXlyN4jOYxM/NAcM75cp8GDeUxcS4IzquZdy7Jg4Yg4jzivIjz3hdp17YtiReX+CwPQSTxuMQr [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.BlueViolet">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA50SURBVHic7Z1LjG3HVYb/f9Xep+8j1w/smxcBR4I4iRwCBIiiKIJYyZAhMS/JDJzIlgxCRjgTILIyIxZISBGRryCjTGJPQDBAYgCDTBASCGKHh8CG2OAEJ1zb93a7z6611s9g73369HW33Rbybce7Pmlrn7Oq9jlVdf7etU6dVv0UxIfveOYeQL8B4L0ADI03Mwngn0n+/m8+/q4v8+E7nv6MoEun3arGacDPdIIeAACKD5375b9meUu9H0AHwEE6JAfokgKAm5lLcgghwUE4BCcZmuoS8EyFmVUAU93N6ziAMCuulJP0jIy5jLQg6Eq5WakRGQSdMCfp4RGAucHcPYKwsQzF6+BBmRuKK0rtrA9f [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.BlueVioletSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAB2gSURBVHic7X15lGRFne73i7iZWXtVd3VDN7Y0a7OKsgqyyaiH0afOm6dPfePOYUZGHXjojNs4M86o4zTu46AOHh741Dfo8JZBEVAQBRsbRKGBZpVmE3qtpWvJqrwR8fveH/ferKzqqq6qvFXVVd35nZMnM+LGjRsR+btx4/6W+AQA1h//7HtF8GEAxwKwaGB/RoDgEZJf+Ojmtd+WK0547mKA39rXrWpg4UHiogjgh9LkPzT/yR0+anN/IUABgAcQADhAPMkAwBtjPEkPIpDwEHgQXkQCSQ+IF8CrMhhjHIC0bHIs+UYwxnoqvYh4DRqyYyImCMRT6Y2xLgQNAvEC40XEBx8CYLyB8d6HIDDJMVjv [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Brown">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA6BSURBVHic7Z1NjGXHWYbf96tz73TP2M7AeMZgUBwEdkAWAeIosRR+7PAjFiCEIltECkYEkC0NCxJFCKHIGcOCsIjEwpIdG+ENm9hikQ0KG4RYRoig4DgCCYfI+cFjj2Y8TP/cqu+HRZ1z+3ZPtz0t7Gl7qh7p9DmnTp1z66v79q2v67bqJQA+9gF8IgKfAvBeAAmdGxkD8B8gPn/uX/A3fOz9+IMgnjrqVnWuPyR+fwjik/UMj536zUcxu+nkWQADAAWhCCgAjXADoCKiEaEIWESMdUJJWoQrQCWg7m4iLADGuvVa3YeJiIaHklA3s/oa9TkENNxVhMXMjIASUBJqWmo7AFUtRkS9htCSszFCBa5h [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.BrownSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAB3SSURBVHic7Z15nB1Fufe/T3XPmn1PEMKWhMQgS9gEAYmAXuFFwQX0Al7BKIgsisiL3oBBEQURFxQhXFleRC6CIIiyRYhi2IMSsqgQAmTfl1nOnO6qet4/uvvMmUkmy5wzyUxyvp/PmXOquvrpqupnuqurq+onAFdP4BwVvg6MBQIq7Mw4YJ7ADd+eyV0y5RAmAbft6FxV2P6Icm4IXJqGvzPoE5Nt2HvgRQJVgCXxlhiwqt4B1hhjVdWiOFW1CBZVKyJO1VsQK2C9984YiYE0bbIt+VZnjLHq1YpgvXMuOUZiR8Cq99YYiZ1zTsAKWBGss3GSD7DWxk7QZBtq4yhyomoN3qqzcWjE2XyLNepsS1Oj [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Gray">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAvYSURBVHic7Z3BjxxHFca/V1Xds7tOdm0n60i+xIdA4BILISGEEMqKK0cSwcUgocSRjIQCAgmBknFuHBIJKSLyCuXEJfYFbhzzD0SRQDEcIKcICTtokL07O931ql4OU9Vb0zuz3gXbE2/VT2p1d3V1T1X1t1Vva0b1EQAaDoc/AvBTAM8CUCicZDyAvxPRW6+//vq7dPXq1ZdEZHvZpSoshZeMiLwKAEQ0PHv2LFVVdQWAAcAAHAALgEXEAWClFIsIA3BhzwCYiLpzImLvvVNK2ficeC3eG58T88ZrROSIiEWElVLWOeeIiOPmnOvKwcwz16y1johiGa0xxjEzK6V4Mpk4pRQ3TcMrKyvN3t5eG1th [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.GrayLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAvpSURBVHic7Z3PjxxHFce/r6q6x7vexT+WdQKXoGixlUskiyzigFAsrnAjEXAwBxM5yEIoSHBBQoYbB5A4hMgW5MQl9gUOSPwPtoVkErIoIrIljGUssusd73inq149DlPVrumdsdeQeOyt+kit7qmu6qnq/m7Xm9er/pKI0JUrV04B+CGAYwAUCnsZD2BNRH710ksvvU1Xrlx5TUTOz7pXhZnwmhGRNwCAiM6+++675Jw7A8AAcAAYgCUi571nInJKKSciDgCLiCMiF9Yc2rhYXylliWhHXQAcj5MeW0ScUqrd1lpbZmallCMi1+0HM7fbWmvnnOO4rZSyVVWxiDhjjGuahuu6diLilpeXh/Pz8008 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.GrayLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABifSURBVHic7V1rjCVHdf5OVfd9zdx57jzuPr278dqWEzkEm0RBSYwgf/iBFCWCRIIILJAdkoAECUoUpEQhkUIURCJBgmw5BjkkhPgPBIQVkSARTAh4Y1hjdu2F3dmdx87OzM575k53V9XJj1vV27d33t0zO4/7SVe3q2533eru012nTp1zPgKA8+fPv4eZPwzgfgASLRxkaAAXAfzNww8//Dl68cUX3wvgqbvcqRbuAojoMQ/Ah2z5z19++WVljPl9AD4AhYa0RESkjDGaiJQQQjGzAqCZWRGRst/aHqPc/kKIiIju2BeAdu0k22ZmJYSIt6WUkdZaCyEUEal0P7TW8baUUimltNsWQkS+72tmVp7n [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.GraySelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABnaSURBVHic7V17kBzFef993bO7t6d76k4SOjAPCZAoAQKZhwAhdCDKZVN+hGBIquxUxeWUiZPglJ24khRViNipihOnnFTihIIi4CJOHCMsIwN6ImSExENI5iUJA+IRAUIIJO65OzPd/eWPmR71rm5Pdzt7p3vsr2pq9uvp+aZn5tvunu7+vh8BwJ133vmHzPwdAAsBSNQxlaEB7COiH95xxx0/oVWrVn0dwD0nu1R1jD+I6GsegG/H8t92dHQoz/P+jIgyABQiawkBKGbWAJQQQjGzAqDjvQKgiCiRiUgZY7QQIrR67DF7rtVj89pjRKSJSDGzEkKEWmtNRMpuWuukHEqpkmNhGGoismUMPc/TSikl [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Green">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA2+SURBVHic7Z1dqGbXXcaf57/2+87kTMZMPiYxEZImqQmxtSitbRVtE6RQRUSwE1TsSAklgVEkigU/CLFX9kJBCIYMEm+86UT8ABWveil4IZWYDyE6aU3T2ozpmWZmzpx3rf+HF2vv97znzJxkhpo5mVnrd9jsvdfHftda53n3+p+1D/shAN79GTwixG8FcD8AQedaxgH8RwB/8upzeJZ3P4zPM3B8r1vVufJE4PODBB4PAAg8+bmPPcGDa4eOITCAUAQMQAGh4W4glBSNCAVgEaEgNCKUpEW4ElQQ6u5GsgAYy7oCVA9XIExE1COUgJqbAVvXGeurCIuaGQkFoCBUtRjG86LFwBjzQnPOFgwNuJpr [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.GreenDark">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA2+SURBVHic7Z1dqGbXXcaf57/2+87kTMZMPiYxEZImqQmxtSitbRVtE6RQRUSwE1TsSAklgVEkigU/CLFX9kJBCIYMEm+86UT8ABWveil4IZWYDyE6aU3T2ozpmWZmzpx3rf+HF2vv97znzJxkhpo5mVnrd9jsvdfHftda53n3+p+1D/shAN79GTwixG8FcD8AQedaxgH8RwB/8upzeJZ3P4zPM3B8r1vVufJE4PODBB4PAAg8+bmPPcGDa4eOITCAUAQMQAGh4W4glBSNCAVgEaEgNCKUpEW4ElQQ6u5GsgAYy7oCVA9XIExE1COUgJqbAVvXGeurCIuaGQkFoCBUtRjG86LFwBjzQnPOFgwNuJpr [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.GreenDarkness">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA0aSURBVHic7Z1fiCVXXse/39+pe2emZ8ftmOmJG3BikjUhJCxKlESUkCCBuIj7sDuDihkJYUlgFFEx4CpD3KcNiCAMDhkk++LLTgQVdPFpH32ThTjJCNHZxGzW3UliT2a67/Stc36/nw/1p+ve7p504+5ce875QHVVnXOq7jl1v12/3617OV8CIO7F8xD8ARwPAhAUbmcMwL/D8Rf4Dl4j7sWXQZxfdK8KC8DxZeJ+vAXHQwBePvPcGS4fWT4NoAKQACiACCCZmwJIQknungBou07unkiquSWSCUAyMyUZu/OYWyKYzCw5XEUkuXkCkVRVh+dpj08URk2qIFJ7nhRT1H47RnW0fYCnuq7V3ZO5paQp [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.GreenDarknessSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAByvSURBVHic7V15mBXFtf+d6ntng4EZdnAFZMsgGhQSt0SifkbMMxIS4CkYUYzKYwcRFRRQNjXyiBEXEtEoKmo0MQoJIdFIeCjKU5DtKci+DMMMAzNzl+6qOu+PXu6dywzM3L4DM8P9fd/9uk919enq7nOrqqvqnB8BADphOICJALoDMJBGY4YCsAWEJ7EdLxM6YgQIi053qdI4LbiD0BmbwegBYObU4VNli9wWowEEAUjY1mIBkJq1AiAFCcnMEoBytpKZJREpzVoSkQQgtdaKiCxXj2YtCSS11pLBSgghWbMEQSqlVLwe53xJgiwllQJBOnqkJS3l7VuWYjhlAEvTNBUzS81aSiUtIlIRMyKVVrK8 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.GreenDarkSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAB2vSURBVHic7Z15nBxF2ce/T/XMXknIRcjBEZNADsMlAgoKggEPRJTDgBwqGF8QIdwRBZFb8IqI3EqCgHK9oMgloiiIHMrLFRIkBEgCISF3dndmp7uqnvePnp6dXXaTzcxsspvM77P7mXmqq5+u6n6mqrqqnucnAKOP4HiEs4DxQEAVmzIcMEeEn867i1tk1BFMEeGmjV2qKjYClBNSRjhTY/Hi4/c83/arH3QqkEawKA6IEKx67xCsiLGqagGnqhbBqqoVEafqrSAWwXrvHWBFJIrzekvhmHPGGOtVrYB13jlo1ZM/3xojkXXOiWABi2CtjRx5ObKRQzR/TG0Yhk5FreJtZEMbpAMbRi3Wi7NNmSZn [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.GreenLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA1uSURBVHic7Z1PiGXHdca/c+q+N/1Hk7SV+ePIyeBgImGMTIIgyiIxUhIHYbIwmBnihWUkJ0gwXiRBZGHMICcbeWHIwmKQlFibZGEpBByICQS88CIEsgnySMIBKRaKHUujoUeT6df9bp1zvizun37d0z3Tg6Npaap+4s6te6rqvqp637wq3TvUJyAEwKMA/gzAPQASKrczDuCHAL4BwbcExB8DePaQG1U5HP5IQLwC4OMAvnbuhXNY21o7C6KBwEA4BBmABcMhMBU1ggbASRoERtJExANhImJDeVHJAAzAmBcMI+iq/X0E5uE+3kfFobBgmCTJ7u5Q2HBkz76YptKQYEy01ltnokUKs2RZjohvYct8 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.GreenLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABrBSURBVHic7Z17nBxlme+/z1vVPTO5kJmQKwjILZANF5GLR5DPISt8XONn+YhI4AhBgnEVDiQQIosQMCAEUHY9rIpcVqMiIqKscuCwy4KsICI3XTAXUWIgQK6Q62R6uup93+f8UV09PZ2ZZGaqZzKT9DefztTzVvXTVdVPV731Xp6fAKDMBK4ADgcC6uzOOGAZcBvCDwRlFnDPLt6pOruGCwVlKTAZuGH+T+bb0cXRlwI5BIviEGLAevUOwRoxVlELOFW1CFZVrYg4j7ciYtPtxUgMWKC8zqu3ijpjSn4E67xzZT9GHAbr1VsJJHbOOQw2fcUudpXLatQSYDVQG7nIaaDWB97awMbSIK6dduvyzrba [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.GreenSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAB2vSURBVHic7Z15nBxF2ce/T/XMXknIRcjBEZNADsMlAgoKggEPRJTDgBwqGF8QIdwRBZFb8IqI3EqCgHK9oMgloiiIHMrLFRIkBEgCISF3dndmp7uqnvePnp6dXXaTzcxsspvM77P7mXmqq5+u6n6mqrqqnucnAKOP4HiEs4DxQEAVmzIcMEeEn867i1tk1BFMEeGmjV2qKjYClBNSRjhTY/Hi4/c83/arH3QqkEawKA6IEKx67xCsiLGqagGnqhbBqqoVEafqrSAWwXrvHWBFJIrzekvhmHPGGOtVrYB13jlo1ZM/3xojkXXOiWABi2CtjRx5ObKRQzR/TG0Yhk5FreJtZEMbpAMbRi3Wi7NNmSZn [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Orange">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAxUSURBVHic7Z1bjCXXVYb/tXad05fJwAyei3CwsUhiE8lgUCSIBERYYJSHyC/RjCASToLxRTJREhA8ALJIHlDCAxIoMBpDwgNByA1EBGEpUqQgeCBCSERx4iQIXxLZBHkGuz2e6TN9qtZaPw9Vdbr6TPdMj/D0mZm9P6l09l77Unvv+vvU7upW/UJA8DAeBPDrAO4CoCjczASAb0HwhziNzwgfwkMQPLHoURUWwkMVBB/tMr+39vbHZXPp0GMkKhEYCReggcAY4SIwETWABsJJWluPJiIOhomIAW19VWkAGIC2DGJkGEjXpAbSBLAId0Hbj4q4tuezpNK4u6vAVGAKmHvjs7Q1rkJLAktC87r2JLQk [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.OrangeLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA6GSURBVHic7Z1NjGXHWYbf96tzb3dP27Ed/4xI+DELHJBQACEhFiDZEUIIBXaOyCaIBJRIkwVEiFUU27BhE4mNZdmGsIFFbISUWEJs2LBASGwQ+UEgBYRAgfgnY2xPd99T3/e9LOqc7ts93c4MyNPBdR7pqM6pnzNV57731td1RvVSAvHSUx+H6TMQPgCgYOHdTID4JwCf54ef/AL10lO/Aei5y+7VwiUg/voA6Lemy6de+K/7cYTVNQEDAQfhUEuVChJOmgNyCCHICbgAJxmQnIQDdGWGmdXpHoG5ruQAwop5q0/PyCBbmRnDSJfkxaxGRJjRje2IiDDCzczDPczohfRi9PAaxejFzAtVd1ZDINzX [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.OrangeLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABs3SURBVHic7Z15kB3XdZ+/c2+/NwswmAFAAAS4b1hMUhJpWlIsa6FJVSpOrCSOJSVVVhIpSllxEillJ64srpIUO1VxKi4llchWUaVIiuJYkeRIoqSQ2ldKFMVVJADuJLgDJLHM9uZ133tO/rj9lhligJnXb4AZ4H1VU9O3+/Z5t7tPd9++y/kJgH3lw+8G+z1gN+AZcCYTgf2I/Gf5Gx/8tNhXPvRe4OOnu1QDTgfyngz43bTMv//s85tDk+yfg9QEgkEUKBCCqUURgogLYAEjGhbKfEFEImZBhAASTDWKSBAnBUakzIukbc67kPJL0KhRhGBmwTmJTiSYWfDOFTHG6JwEJ+kvxhidEJxzIYYQnZPg [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.OrangeSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABsLSURBVHic7Z15mF1FmfB/b51ze006K1lZEsSwBEREUfRBZRARUQSUZWAYFoX4jeiguDI6MqgwzMf3fbO4IIwC4gIGRRZhFAYeQAYYdkiIIRMIECAJ2Tu93HOq6v3+qHtu377pTrr73E66O/f3PPe5p+rUeU+duu899Z469dYrALqAc1AuAvYDIuqMZRywBLhSruZ60fP5DHDNTq5UnZ2BcK7o+TwP7I9w6Y3zvmmLTZM/DxREsKo4gRTBqvdOBCtiLKhFcapqQzm1IuJQb0XEQihvjKSABcI+xKp6i6ozkbGoWgHrvXNCkGNEnAnns5GR1DnnjGCNYA1Y51JX3rapM6I2Emwkal2SuEjURuJt5G3a [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Pink">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAzUSURBVHic7Z1diGVXVsf/a+1zb1V1pbWbpLsxmUkH1ISBOIwM6CAyJDgOQXwQtIMKtoQ4JBBFFJwHlRDnyXlQEIIhjcYX52E6w6AMijDgw7zMwDwIk07ig+kxRKNJk6n0R93UPXut9ffhfNSt6qr+YEzdTu39aw7nnP1x7t77/uvsdc9p9l8ICoAnAfwhgIcAKCqHmQDw7wD+EsBLQvALAM4tt02VJfEFIfgagE8AeO78s+dl69jWMwQbgRiAcWPQBWKiYiAMgJM0gRhJExFHwERkLK+iua8/5jFoIFxVDYQJxMLDh+uoqCvUGLQkKbu7K9SGzfP2uWd3pVpCssRk3ronJkuRLFnKK7Li2IJNfWp2 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.PinkLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA5fSURBVHic7Z1fiGZHWsaf961zvp7pTpvMhsmoq2SRmFWQxUUjXogkKOKFlyb+uRiRuCQQRSLojbKEvfNCwYsYMmiu9CKTRRRvBO+8Ucguwu4mLujGDatZ4rDbyUy6p79T7/s+XlSdr7+vZzqZGGY6TtWvOdR36s/pqjpPn6qvTlOPkBR88ctPgvg9AJ8GoOjczQSArwP8Uzz+ky8JX/7y5yC8dNq16pwCgs8NED5bTuS5y+98TQ5pzxAYBDCIGEgDxMhwgZio1jg4QROIkTQRcQA2l2OEq2oGxAA4Sh4jaSBcU7mOiFh4+Jymqq4ixqCllLK7u4qaipiKmHu4ipiqmpu7ilhStaTJPJvPnxM0bw2j [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.PinkLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABq3SURBVHic7Z1rjCTXdd//596qnp6emd2Zfe+Sy6e4fFPW04mFJBIkffEHA3FsKQEsw5YVUHESC7ATI4ENJIgdIA5iKAGsRKAgS4riWBb1wWZsULAVK7ZFmW9qSS2XD5HcJXeX++I+5tXTVeeefz7cquru2Z2dR/fMzqN/QKP73rp163W66tS5j78AAB9+5hdB/hqAuwB4DNjMBABHQfwX+eT7vyZ8+OnPgPjS9d6rAdcBkU8nIH61SP6Hb1x8QVti/xJAKoASCALkgChpQSAqzilIBRAIqkCUpIpIAKACKESUZsE5lwOiAAJiGSWpIILzsR4RUQsWymXOueBElEb13uchhODEqRNRJ6IhWHAi6pzT [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.PinkSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABkHSURBVHic7Z17lGVVfec/v73PvfXoV1XTb0ReApKmjSKSSQhrIMDKhKxxjTpAJqJRJONjFAwSJ0bI4AvRJOM4iQZxDD4SNWIcJRoSo2EIYgABg9DdEulAN9BNd0M/q6vqnrP3/s0f+55zH11VVNW51V2P++l1V529z+nfPY/f3Xuf/fh9BUDRNwPvAV4KWLrMZzywGfgjQb4gil4JfPYon1SXo8MVougm4HTgg1+97quutrz2LqAiiFPUC5IBToN6QZwYcSgO8KrqBHGq6kTEE3Ai4lqOF8mA1n1evTHGoThBXPDB53aMGG8wToM6Kzbz3nuDcfnHZ420z7w3apzFOqvW+dR7q9bZYB0prt/2O0Zx [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Purple">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA3QSURBVHic7Z3NjyXXWcaf5z11b39MxjN4ZhzwIjICHJAgEYoEWQCyIyEhxAIJ2SKLGCkB2dJkAVGEEIrsGdiERSQWWKOxUbJhE3uVDWKF8h+AguMIJCJQECS2R22Gmdtddd4PFqeq+t5299ADeNqZc36jUlWdj3vPOfV0nXeqrs5DALyO658NxBcAfBRAQuNhxgD8I4CvXMO1r/I6rv9eIF4561Y1HjwEf7cLxB+M59cvvXgJi4uLqwA6ALq+hYcBUBHRiFAANu4VASVp4aEglKC6uwklj/XnvPBQBGz6HILq5gagnJNGUMNDhZLNzAjqtFm20g6IalZjjHlBzUM2BlVcNDRyx870QFVM9ODOgckg [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.PurpleSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAByzSURBVHic7X15mBzFlefvRWRVdbX61IkECCOBjk+c4r6EhMTnNXx4bGwL7xq8A4vXmMVgA2bBBiP5wODxjMfrsY1hGPBgfCEQMIBOJIEsMIcwhw5sJCShWwJJfVVVZhxv/8ijq0t9Vla3ulv1+77qzBcZ8TIi83VmZES89yMAmIu5VzP4FgCTAEiUMZhhAKwn0E/uxt2/oTmYcy2ABw91rcroexDoGgfAzYH8vWF3DtPOUOfrBEoA0PCtRQHQbNkA0EIIzcwagAm2GgxNRIYtaxA0gbS11ggSKtQTHmPLGgwT6iGQtsYaAL5MZAik2bIWJJQxxhBIhz+jjF8PCK2VNsTBMSatPGWISQsrNGtWDjlG [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Red">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAzOSURBVHic7Z1diF7Hecf/zzPzvrvatZoV1gd1WjmQ1CbghBZDY0oxNm2KCb0IpBJtISrGCTa4pSRQX6RFuLmKL1ooiAqL1rlpLyKX0lIaCoVc9KaFXhQcyepFo8a4TWIJd2V9vNr3zPPRizPz7tlXu/IK432tnfnBcM6Zj7Mz8/515vEcc/7kAAF4DsDXATwKgNHYzxiA/wTwpwBeIwe+CuDcYvvUWBBfJQfeBPBpAC+fP32aNtbWXnQgEiAOKAEJgLiZEiDELHAXAOruQoC4uxCRwkyISEp9JkoABMCszM0E7sr5PgSIqWq5DxMp9+0lECVVVQakJE1Jt5y7SwAkuIt2nQZ3CWYSRNISkWJjQ8aq [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.RedLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA0ESURBVHic7Z1PqGVHXse/v1/Vve9fOp1OprsxoyQLnVGQGUUQEZEERVy4NEFdtEgcEogiEXSj2K9n50JBCIZuNCtdTEdEcSO4c6PgQnBMdOFERBlJmvE63f1uv3Pq98dFVd173+33ul+Pk3c7XfWB4pxTp+q+qrrfd+r36jzqSw4Q9vdfA/DrAD4PgNF5kjEA/wqi38fVq++SX7v2Jbjf2HSrOhvhSxHubwEAiPZvPvssHU4mbzoQCRCsJHdXAoSYBe4CQN1dCBAHhIgU5RpE4mbKzKnUX9zzUpfL5xCRmJnWe0ykTCTuLoE5qaoykdSkqsqAMLOoiDKRhJI0JQ1EEpgluKetGBUiMmUWOTzUbWah [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.RedLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABoFSURBVHic7Z15sBzFfce/v+7Z3bdPepeeDiTuGyIOI2PAHAKBKJft8hHHQFJlJ7HjlImT4JSduHK4CoidqjgVl5NKnFBQBBzimBiwDQaLG2EBBiFkLknGIEAWSEgCHe/a3enu3y9/9Mzs7OqdO/ue3rGfqq2d7unpneO3M7/p4/clAJDrr/8cRL4K4BQAGi1mMw7AFhD9M1177fdIrrvuCwBuOtR71eIQQPT5AMBXouTf397baytB8OcgyhFgBXAEGABWRBwBlpSyELEAnIjYqJwlIocoDSIrzE4pZQBYAMk6ibZVUT1EZJnZxesUkVNEVkSsVso455wisvHHOecUYJVS1lnrFJHV0ccZ4zSR1UpZ [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.RedSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABqUSURBVHic7Z15lBxVvce/v3uru2cmM8lM9oQ9gUBeAsgWnwLnGSFHjUePgASfBCQYZXkQIARkSTRsART0qYgBxMhDdkGJLLKZA4jIIrIkGZHELITs+yzdXXXv/b0/blV1dWdmMtPVnczSn3P6dN9bVb+uqv71vbfu8vsSADAwDcDlAA4DIFGhN6MBNAK4jYD7iIHpAO7ZyydVYe9wLjGwFMBYANc/PHu2yg4ceDGABAGKAU2AB0CxMZoARUIoMCsAmpkVAYqZFRFpGKOISAX7CyIPgAIQbmNjFJi18O0QoIzWOrAjiLSwxytJ5GmttQBU8NKep/M+MysJKMmstOtqyaykMUoq5aWINDIZldRaqeZm [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Tan">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA6+SURBVHic7Z3PryXHVce/31N975sfniTGGiKBhFlAghdEQkiIBUK22AILiC3YGISDbTA/FBBskCKLHQuQUGRbNtiwgEXsIMGO/wFFIFCisMCrCCk4MFbG8zy365zzZVHV9933Zp4ZCzwvTNdHanV3dXV3nerv7aqu91RfSuKX/+KlZyj9DoBPAzAMHmQSwNcJ/MnP/cpvvFF++Pse+VUCfwbgOgBecOEGHz1Ee9Y/+7V/+odvTAA/31Nf/C+fKNgLACYATsAFOACXFADczFySAwhIDtIlOcn9PgFPKYysIB1S9DwuyQVE6dch6RERyzEzi36+F7MamUHSlyXcg6Sbmfuy3fdrrbFsA6jTNEVGeDHz [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.TanSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAByUSURBVHic7X1rrB3Xdd73rT3n3AffIiVKst4PihQp62HKku06lmM3Qf3DQBHDaYG4SFwbdpPUBpI2aNEALZoWaIoGblM7MeTajuG6dWMDfaSpX3Jk62GTFC2JpCTqQUqUROpFiaTI+zoze6+vP2bm3HMvecl77zmXl5c8H+7BnT2zZ82ePWtm1qy91/oIAN/52n/+LQK/D2EjgIA+zmckAHsh/oePfep3v8HvfO2Ln6L0lcVuVR9nHyI+mVH6vbKkf/1WESIt+8cAGgAiSm0pAERJCUA0syip3CZFkFFSJNkuE4guJSMLkBFSqupESVFACpUckjGllOptZpaq/WMwK5J7IhnrX4oxkYxmFmO9XJWL [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Turquoise">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA2kSURBVHic7Z3Pq6XJWce/z1N1zv3Rmel2+pf2IoyoEwVNkIBmodITEERcCNKDWThCEpmBzkJDEJEw062buAi4cGh6RpKNm/SsshFXkv9AiZMOCgYloknPNHfs3Hvufd96frioqnPfe/rem9tq95101Qde6n3rrXpPVZ3vOfWc9z3UlwAQbt78NNw/D+AjAAI6TzMK4J8BfBk3bnyFcPPmH8D9zdNuVecUIPpshPsflcObr50/j3Oz2XUAEYBMN3NXAMLM4u4CQEsqDggRqbkLAQIiMTMl5lTqL8+Zuzigy+sQiZopAHF3ISIFkZi7EHNSVQWR1C2pKgABsyQR9ZLvRDKmpE4kxizinihG3RMRZZbt [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.TurquoiseSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABxSSURBVHic7X17mBXFte9vVfXee/YwT2Z4qhhBeXyoKL4fICh+udEvOYlJMPdGc49ecmO8BhM1Xk0wQh5Gc3JOTm5OEqPHoznGvERRj8pDRCJB4wPjg4eJICBvUGBe+9FdVev+Ud09PcPMMLN7zzAz7N/37a+7qqtXV3WvXb26qtb6EQBgwYJrwHwzgIkAJEoYzNAANoDox7jzzl8T5s+fA+D+I12rEo4AiK51ANzkJ787r65ODXWcr4EoAUDBaosHQBlmDUAJIRQzKwDa3yoGFBFpw6wIUCBSxhhNQniBnOCYYVYM6FAOkdLGaACKmRURaRApw6xICE9rrUGkgp+ntQagIITylNLs5zORcj1PM5Ey [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.White">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAARKSURBVHic7d09aGRVHIbxZ2ZjJMFGIXGmcCEaBCFYWPhBZElhIYOIhYWdaFywFASDaYyLzdooihZ+Fqsx2KTJNmn8hkEIJuKqWSQQMKjFRlhws5k4jsW9w16vM1a551zv+/7gwORMiv/MPntzMykOQA14Cvge6AI9r0qvLnABmAdqNeA08Bam6DQk//N7wAvAS8BV4lda5PoLOAc0gBvT1QTeAP4owXxFrQPgbWAKeDHduwDXLvtnSjBkyPUhiVHgsxLME2p9DdyWPu6SeaLK9Q9b9wHPlWCO0OuV/uMRrhlHzyPAw7GHiOCx/oORYd8xNzdHq9UKM04Am5ubLC8v57dvAW7PbtTrdWZnZ4PNFUK7 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.WhiteSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAtuSURBVHic7Z17bFRlGsZ/M50OOwUtpZ3KLSBN1XRBXbxgELWCS0C8rBpxhSxsShZDU1SCtmDRFqxpUS6dKrBJccGywxpcUUnUZNk1AXYJAZOFhpuLERoI/QMJF7tyaTv99o8pbCkzpZ057/nOdL5f8iXTmZ73efr1mTlnznxzXghTABwAWgFlRq8ercB+4PcALuAPwFoMycgsgEOEk7EEWAxcQH9KJUcIqAMGAhntYxBQA5x3gD+p0QSsAYYDFe33HaB9QhSw1AEm7Rx/JcwvgH86wI9dYw/wy/bbITo8cMkB5uwevwbKHeDD7vHHK7dd7TeSldXAb4Chuo3YzBlgAIAn2m/k5+fz1FNP2eZImn37 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.Yellow">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAyxSURBVHic7Z1fqGXXXce/v99a59x/GTtDMjOYtJmANaEQS6WgRaQkWEsQHwSdUAVHQloSSIso2AeVEPtkHxSEYMig8cU+dFKKUhSh4ENfWuiD0EwSH8zUEI1NhvRk/tyTe/b6/fFh7XXuvmfuvblDmHsyd60PLPba68++a63znb1+2TvsL7mDADwO4I8BPACA0TjKGID/BPDXAF4gd3wJwPnljqmxJL5E7ngFwCcAPHPhwtO0tXX8KXdEIog7lAgJgLibEkGIWAAXAOruktu5EJECJkQkpT0zJQACYF7nbgK4MufrEEHMVMt1mEmZIe4mIVBSVWWGlKSadGfeJQRICC6qnYbgEoJJCJJWVkiBLRmP [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.YellowDark">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA2RSURBVHic7Z1fqGbXWcaf5137+878ydiJ6Uw01aRoTCjUYqnRIlIS/EMRLwqaoEIjJZYWooiCvVApsVf2QkEIhgwab/SiiYgiiiB44Y1CU4Q2/0AbE2pbkyE9yUzm5Hx7ve/7eLH3/uY7M+ckM0jmJLPXDzZ77/Vnf2ut75m13tnfYT2UQHwFDwD4bQB3AjA0rmcSwHMQ/hg/iseor+DTEM4cdqsah8KnqSfxDIAPgHjo8ac+z10/+aCAjoCDcGg4KzNIOM0ckgMISU7ChzMDSCe4Lm/GCsIBBJRO0qV0QGHjc0h4ZsT0HDOGES6ll8IaEWEGNw5HZA0j3AweUcMoLwYvRR7eRzF5KenFvG4tGNCu [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.YellowDarkSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABs+SURBVHic7Z15mCRHdeB/LyKrqrune6a7557RLSShHYlTyGtjvkUL+mzLn/kWWARrBAYsL8eCAFmwBgQW92Uvy9pgkBYLzH2YNVqwWFaY5RCHkMA6R4CkuXrue/qszIh4+0dkVlf3dM90d1bPdM/U7/vqq3yRkS8zo15FRsbxngDoPbwc5QbgIsDS5lTGAw8DH5bL+Izo3VwL3HKSL6rNyUB4hejdPARcDLzrS/ff6Oqh/3VARcApeIEMwWkIXgQnxjhUHeBV1Yng4rd4CE4QV+Q3RjIEB3g0OBFxqsGBepPrEcGF4H2hxxjxRnCqwVkrmffeG4MzEj8+ZN4Izhic95k3os4anLXqvEu9NeqsDc4a [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.YellowLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAA4dSURBVHic7Z1fqCVXVsa/tXbVuf/sSTpJp82MmnlwMgZkUIRBZJAERXwQn+z456FF4pBAFFHQF0XivPmg4EMMaTRP+pCbQRQVBudtEBQUBpxJnIdJi8wYjW36Jt25p++pvdb6fNhV5557+t6ebjLpO+m9f1BU1d676uxd9Z3a6+w67E9ICq5+/mkIfwvAJwEoGvcyAeBrIP4YD1x4Wfj2K5+FyKXTrlXjFKB8Vnh193UAj0Pk+d0vvCMHCz5HohOBkXARZABG0gUwUTWABsIJWClHExEHyz5EjBGuqhkCA+AoZYykAXRNyaa0CPcpT1VdRYwIS5qyh7uqmIqYqpj7uK9q7uaqYimpJVVzz56SjvvI [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.YellowLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABp/SURBVHic7X1rjCXHdd53qrrv3Ht3ZndmuQ8+xDe5fK4syhYdSTAiglJ++IcBJ5GUAFYSKQqsOAkV5GHkYSBB7ABxEEMJEicCBYVS5IdC6kfMWCDhSJZkiTJFUSSXy+WSJlckxbeWu0vuztxHV9X58qO6+/adnZmdmb7zvh9wZ7qqu8/tx3erT5+qOp8AAM/c90mQ/xTAjQAsxtjOCACOQ/ifZObjXxaeuu/TEH5ho49qjA2AyKeEp+99GsBNIP7dVx884/uZ/CMAqQg8iSACB8CTDAJ4McYD9CACAR+3oxeRAMYyRDxVgzHGQeABBMRtPEkPMBhrfVGnGkKxzhgTjIgn1FtjXdAQjBFvRLwx4kPI [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.Tile.YellowSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAIrQAACK0BabLJ7wAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAABjhSURBVHic7Z17lGVVeeB/e+9zbz26qruq6XeLvKSBNG0UsfMgrIEBViJZcY0aIBMfE7EzolFQJE6MkMEXoknGcRIVcQw+EjVijDIaEkfDEGwDLaAITYvSQjfQTXdDP6vr1j1n7/3NH/uce8+9XVVdVefeet7fWnfdvfc59dV5fHe/v+9TACK8EXgXcCZg6DCfccA24C+U4vNKhE3AZ2b4ojrMDFcqER4FzgLe/5WvXG+r1aVvB0pKYUVwSpEAVsQ7pbBKaQtiASciNpwnVinlwFullG08XyVA0zHntA5ylMJ671wmR2vltMaKeGuMSpxzTmts9nEucY1pscZgjRHrXOyMEWuMtxDb3l5jYcSWy85a [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="T:LaavorCheckBoxRadio.TileRadio">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Aqua">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nFXFmfe/zzn33t43umn2TRFFREAak4nRwS0CIupMJBrjJ4k6ISaTSSYxmSS+GU1MXCav72TMNpnsmUwScQaFbnEfo9Go2BhBQFnCKjTQNDT0eu85p573j7oX7tZwG3o3v8/nfk53VZ1zqk499dRTTz31PMJQg6oAk4CpwGnARGA8MByoAiqBvPivMH5XOxCN/w4ATcB+YCewHdgKvIXItj5qRZ9B+rsCpwzVMcAFwPuAOcB0oKSL0h62c9uBADgSTy8FXCxBVALhLu4/ArwJvAb8EXgJkT2n3oj+w+AjANU8YC4wP/6bklZiB7AWWIcduduBXUADIkfIBaqlwCgs55iI5STnYIlr [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.AquaSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15fFXVufe/z97nnCQn80DCECZFFBEBCWpL9eIMiKi9hWqtbdW+dejtcFvb29a31db59u1tr7XaXjvZWWxRScSpXmudqoIDAjIVwjwkgZDhJOfs4Xn/WCeQMwQSSHJO7u3v8zmfnay19t5r7/XsZz3rWc8g/E+DqgDjgUnAccA4YAwwDKgAyoGc+C8cPysCROO/RqAJ2AtsBeqBTcD7iGwepKcYNEimO3DMUB0FzAI+CMwEpgCFPbR2MIMbATygJV5eBNgYgigHgj2c3wK8B7wJvAq8gsjOY3+IzGHoEYBqDjAbmBv/TUxqsQVYCazCfLn1wDZgFyIt9AaqRcAIDOcYh+Ekp2CIa2xS [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Black">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15eFXV1f8/+5x7b0aTQBiUEMAqIEZEGgarYsGhhUAZWozG4UWBCuhr66vWOrxWKor6+vNXa21tX6dqrVWsIElAECngDASFEDBBCmHWMCWQ5Obec8/Z7x/73OROITfzgN/nyXNy9z7DPmevvfbaa629lqDrQQBnA0OA7wEDgH5AT6AHkArE2H/x9jXVgMf+OwIcBcqAvUApsAv4CtjdNq/QdhDt3YAWQBpwKXAJMBIYCpxRz7kGqnOrARM4YZcnATqKIFIBZz3XnwC2AhuBT4FPgIPNfoN2RGckgBhgLDDB/hsUUr8HKASKUCO3FNgHHKKuwxtCEnAWinMMQHGSC1DE1T/k3BLgPWAF [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.BlackSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z17XFTV2sd/a++ZYbhf1RTwmle8EaKW2kGtjqKidowkLRN9Q+2t05vmUXtTyzR9ezt1Ol46x7I0j6kZKqBp5lHzloo3RAU1xAuaooAIDDP7st4/1h6YGzDA3Ojt+/nw2cxae2avvdez1+VZz3oegt8eBEA7AF0BtAfQFkBrAM0AhAEIBeCl/Pko36kAoFf+7gG4D+AugOsA8gHkAbgI4KprbsF1EHcXwAGEAxgA4AkAsQB6APCv4VwBrHIrAEgASpX0AAA8mECEAlDX8P1SAOcAnABwBMBhALcafQdupCkKgBeAOADDlb9OFvnXAGQByAZ7c/MB3ABwG9UVXhcBAFqCtRxtwVqS7mDC [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Blue">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15mJXFlf8/533vvb3SC82+44IiIiCgjooBl4RNlowScXk06IjoZMwkxnGZRKNxG3/+JmNMMhljEo1jIiYodKMiGsUlKjSGXUACzb5DA73d+y5n/qjbzd0abkPv+n2e+1R3Vb3vW+9bp06dOnXqHKHdQQXoDwwETgH6AX2AzkAnoAjIiP6yoxdVAeHobx+wH9gDbAHKgI3A5yCbmuklmg3S0g04eWhP4CLgQmAkMBjoUE9lB9O5VYAHHI7m5wE2hiCKgGA91x8GVgJLgL8CH4HsOPl3aDm0QQLQDGA0MC76G5BQYTOwAliFGbllwFZgJ8hh0oLmAd0xnKMfhpOcjSGuvgmV1wFvAG8C [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.BlueDark">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15fFbF1ce/Z+59nqyEhCWAbAkgKotUEWu1WLCKgoioJIJoXevSvl1ea+1irVSr1qq1tdtba3frklgUQpG64UZBARc2ASEJYQ9bgITkee5y3j+eBPMsgQSyt7/Ph88TZubee+bO786cmTlzjtD5IAy9IhfXnILoIFRygAEIPVF6AN2BpNp/qbXXHAJCtf92I+wBKQfK8LUUkWI8/ZhNhSWtX52WhbS1AMeNoXl9ceQcRM8GxgAjgS4NlHaAPSiHEDzgQG16BoqFkEqEIIEGrj8ArARZivr/xrUXsfnZbc1Ym1ZHxyPAkIlJuF3GYXQiMBEYGlNiE7ACZBXqFyOmFNXNWMHtbPj7gfgb [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.BlueDarkSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15fFTV+f/fz7kzk0w2kgBhhwCKyiIioFaqBXdQEZVEcN++Lt20bq1alWrdqtaWWv22WlutoiaKsohbKbgvLCqbgJCEfYcAWecuz++PSTCzBAIkmeH3+31er7ySnHPuvc+d85lznnPOswj/90Hoc2FPHHMUor1QyQe6I7RHaQe0BVLqftLqrqkCaut+tiJsA9kMrMbTMkRKcPU7VhWXtv7rtCwk0QIcNPoUdMGWYYieCAwFBgCZjbS2gW0oVQgusKuuPAvFQkgjTBB/I9fvAhaCzEG9z3B8n7Lm1fXN+DatjkOPAIeNTMHJHI7RkcBIoE9Ui1XAApBFqFeCmDJU12AFNrDi5V2xN4z3 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.BlueLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15fF3Vde+/a59zr3Q1WrYs2xiDGRMwDmDLhEBJMEMAA44ZLIfplYS+R4amSSntS0NTSGgGmtKBvCZN2wyvpQFkYgaLIRBwCLMtmVFmjEfwPGDN95yz9+of58rWHWRLtmb6+3z0uVd777PPOnf/ztrT2msJYwyqKksaOcIKxwkcqTAdOAxhIko1MAEoyvyVZC7rANKZv+0IO1C2KqwXZa3CanG8UfdxWTMczzSYkOEW4GBR/7JOVcvpopyGMAdlJlDeS/EQ2EHc4BZoyaRXAB4xISYAiV6ubwFeU2WFEZ6LDM9eMUs2DtjDDANGHQEefkeLWls4U5QLgAuAY3OKrEN5VYXXDax2ylqB [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.BlueLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic5Z15nBxHefe/T3XPzM7sfWh125J8YFmWJUsrY3DAlvEp20LG1gpfxEDe15yBEEIIDrGBcIU4EJNAyAG8icH2CsuH5AM7RoBvaSWfki1b6L5v7TU7fdSTP2ZW3jlW2l3t7ozy/j6f+cxMVXV3ddevq+qpeg7hfxlUVZa0MjkUpgpMUZgEnIQwCqUBqAdimU8ic1gXkMp89iHsR9mjsEWUTQobxPJG87tlYzHuaTghxa7A8aLlZR2vIeeL8l6EOSjTgco+ivvAftINHgJtmfQqwCFNiHog0sfxbcBrqqw0wnOB4dnrZ8mOIbuZIuCEI8Cjb2usvY0LRbkCuAI4PafIZpRXVXjdwAarbBLY [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.BlueSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15eFXVuf8/a+9zTuY5EIYwiSKITAKCIl7AoRAQtFUUZ9GfiF6r16p1uFXq7PVn22tR22ptUasVLSIJKirFCQcGZQijCGEeAiRkOMk5e3jvH/sknCkkgSTn5N5+nyfPzllr7b3X3uvd73rXu95B8b8OooBeQD/gJKAn0B3oAOQCOUBC4C85cJIX8AX+DgKHgAPADqAE2ApsALWtjR6izaBi3YETh3QFRgFnA8OBAUBaA40NnMH1AhZQEShPB3QcgsgB3A2cXwGsBZYDXwFLQe058WeIHdohAUgCMAaYEPjrE9ZgO7AGKMb5ckuAncBeUBU0CZIOdMbhHD1xOMnpOMTVI6zxJuAD4EPg [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.BlueSky">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15mFbFlf8/p+593943lmYVwV0BcQFjNBrcxSBBYyNqnNG4gJqYjGMymXEyEk1ijDqTMT9FUKOTGJduBxdwiUvUGOMCqCio4MKmNHQDDb2/d6nz++N9G/tdGrqbXnG+z8Pz0lV169a99b1V51SdOkfY06Aq35nLGONwsCr7iGE0MAplMDAIGAhkJf7lJq5qBGKJf5uBLQJVwDqFNaJ8pg4fVlwmq3v8eboZ0tsN2F2UzdUR4nCsWo5BmASMBwraKO4DW4h3eAjUJtILAYc4IQYCkTaur0V4X5TFFv7uurz28CWyocsephfQ7wgw5XbNKshmslqmIEwBDkgpshZ4T5TlVvjMCGs0YH00 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.BlueSkySelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic5Z15fFXVufe/z9r7nCQncwhhnhxQEMQBuK1Uq9ZWsUqxJYhVe7UOaG3t7Xxbb1+p3o7W215bB9TWttcxWBxAtHqtWrVWxSoqKojMkpCQgYznnL33et4/ToI5QyBAhpP3/X0+fA5Ze+29197rt9d61rOeQfh/DaryhVuZZBymqHKIGCYC41GGA+XAMCCn81+k86x2INb5bxdQL1ALbFXYLMpGdXh32WWyacCfp58hg92Ag0XlrTpGHOao5QSEWcB0oLCH6h5QT6LDA6C5s7wIcEgQYhgQ6uH8ZoS3RHnVwt9dlxfvv0R29NnDDAKGHAHm3qQ5hbmcrJa5CHOBySlVtgBvivK2FTYaYbP6 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.BlueViolet">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nJXFlfe/p57n3tu3V5qm2ZtmU4QGNUGNMZqIa9wxE5kkxjcmzrxmmTiJQdRoYkfHBXCZMZNkMpP1Td4xwXmNCiGO+xLjBi5AsyhC083erE0v9z5LnfeP2419l4Zu7JXM7/PhA1TVU0/Vrd9TderUqXOEowyKyj0z6ia4qlPVOhNVdLxBxilaDgwDyoBY25/8tsdagCSQRNiFshuRnaB1qlIrlg0SCdfcsGL8xv7pVe9B+rsBHxb3n1A/JvTtJ1TMaQInK3YGSFEnxX1gN6kBD4HGtvRiwCFFiDIg0snzjcBKhTdE9S8ayss3rhu3ted60/cYdAR4cPJ7sZZY7EyDXKDoBQLHppeQ [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.BlueVioletSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7X15eFXV1fdv7XPOvbk3M5kYMjKHBFAB5wGcQUW0inUsrW8/bG1taxGcqlFfRQa1tVVr+1btW1sV+qKSaK2oOCEO4AAJMxkZQgYy3ukMe31/3CTkDiEJJLmX9/t+z5Mnyd77nL3P2evsvdbaayD8LwOD6fHJ1Xkqcz5LZTQT5wpQNoPTAKQCSAFg7/hxdlzmBuAD4AOhAYxGENUBXM1MlSRRTpq1/a4tuRWRearBA0V6AMeLJ6fWjLIMeRaTOJOAGQw5GaD4HpobABrhn3ALQGtHeQIABX6CSAGg9XB9K4CtDHxFzJ+xRRuW7Mw+MHBPM/Q44Qjg6bG77W67faYAzWbwbALGB7agKoC3 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Brown">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15eFXVuf8/79r7nIwkgQCKjIpCFREx4FgtWK2KiOhV6lB/tWqL2tvqba115oAj13rb2tHb2uHWW5W2VAbRqtS5iCTMoChlnkwgkInknD289499EnKGQAKZ+/s+T55zstbae7/rrO9+1/Su9xW6HyQylmPF50TgOIUhwCCUPgi9gUIgI/6XHb9mPxCN/+1G2YNQCmxB2ARssAwfPfghG9u7Mm0N6WgBjhSPjKK/a3EOwtkoYxFGAj2aKO4Aewga3AMq4+l5gEVAiEIg1MT1lcAqhCWi/EN93o8sY0erVaYD0OUI8PTxZOzNY5wKlwCXAMOSimxGWImyGtiAYZOlbPUsdkYWNzT4QRE5 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.BrownSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15eFTV3fg/33PvzGQjCQSCYV8URECWgPsCihsgYF+h4lIt2uLSqm2tdWfAldfaWutSrdraWhdUlEW0KgVXRBL2XWTfJBAg+8xdzvvHJDGzhCSQZCb9/T7PkyfJOefee+6c75z1uwj/fYh/KN3FpQ/QQ0M3oAuadghtgSzAV/mTUnlNGRCo/NmP5gDCPmA7wlZgs6FYd/83bGnul2lqJN4VOFYeGkBH2+BMhDPQDEXoD7SqpbgFHCDU4A5QVJmeDhiEBCIL8NRyfRGwCmGJaL7SLl/6l7G70V4mDrQ4AXjqeHwH0xmmhUuAS4BeEUW2IaxEsxrYjGKrodnhGOzxL65u8CPiP5V0wyHH [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Gray">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7X17mFTFte9v1d69p3veAwzSwPAUCMhIfEaPT4zGFxrMjZwY43dMzL3kcWISoycqyNQAChzjSQ65SQ4nj5t7442KuUYYGDgqMcb45CGDOKIiT2HGAebZPd29H7XuH3v30L27B2Zg3vr7vv66u6r23rV3/XbVqlqr1iIMPZCUciIRTQcwiZknABgHoBTACADDAeR4n1zvmHYACe9zFMAxAA0ADgDYB2CPpmnvPvTQQ3v78D76BNTfFThdLF26dIxt25cA+AcAFwAoB1DQSXELbuO2A3AAtHrphQA0uIQYDiDQyfGtAN4GsJmIXmXmV6SUh3vkRvoJg44AK1euzGlqarqSma8HcD2Aqb4i [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.GrayLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7X17mFxFte9v1X70Y6bnkSeTEIHkRMUQIzM9SUBUoiACIQav5KrId3x91/fjKHpU9IBwfXAQPQevr6sHPeoVjJ4IJCCCGJRXnOmeCSHD+wRIIO/XdPdMd+9dVev+sfee7NnTncwk8+gZ+X3ffN1TVbt27a7frlprVdVahCkGZqZMJnMaEZ0OYD6AUwG8gohmMvMMANMBxPy/pH9ZP4Cy/7efiA4w814A25n5eQDbtNZPLFu27LlxfpwxB010A04UmzdvnquUej0zn01E7cy8GECqSnEXwAF4Ha4A5Pz0BgAGPEJMB2BVuT4H4DFm7iSih4UQD7W2tu4ctYeZAEw6AjzzzDOxXC53LjNf [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.GrayLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7X17lFxVlfdvn/uoutVV/Up3kg5JgITwSkJMd3USEZUoiDyMYT7I6AhrUGZ9jK/RcRjHkXFQGR+MMio6OM5D/cYHGmYikIAMDAblFdKPJJAmQGIS8k46nU7Xu+qec/b3x73VVFdXp7uT6q5qlr+1at2qc86999w6+56z9z77QXiTgZmps7PzXCK6CMA8AOcAmEtEzczcBGAagID/CfmnpQBk/c9xIupj5mMA9jHzXgC7tdY7li9fvmeSH2fCQZXuwJli69atZyml3sbMlxJROzMvBhAZobkLoA/egCsAMb+8FoABjyCmAbBGOD8G4CVm7iCi54QQz7a2th4q28NUAFOOAHbu3BmIxWKX [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.GraySelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7X15fBzFmfZT3T09M7olS7JHsixZxiY2Fo6NzREu20C4HELyBRZDEiDsfk5IAmyWJICNVbIN2EvYJM7m8GY3CV+SJRFZg0+IicPhYHB8YIGRb8unJHSMrhnN0d317h/dI+a0JHs0M8qX5/eb38xUVXdXdz1d9b5Vb70vw98eGOd8ImNsKoBqIqoCMAFACYBiAGMA2K1PlnVMP4CA9ekA0AmgDcBJAMcBHJNlef+TTz7ZlML7SAlYuitwvlixYkW5rutXAvgUgDkAagDkJiiuwWzcfgAGgF4rPQ+ADJMQYwDYEhzfC+ADADsZY9uJ6G3OeXNSbiRNGHUEWL16tb2rq2suEd0M4GYAU6KK [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Green">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nFTVte+/a59T1SPdDc3UzGoAERERUOMUMJgIIkOudsTho0GuiLmJuU5xpgQnrtd3EzOYPDPH60ASFLpBFFFREWRQaBEBCTQgg83UTQ/VVWfY749T1XQNTXdDz3m/z6c+VbX3Pufsc/Y6a6+119prCR0PQoDTEIYAp6MZAPQDugFdgVwgJfJJjxxTBYQin0PAYaAE2A0UAzsw+IJH2NmC99EikNbuwCnjcXpjczFwETAaGAZ0qqO1hTe4VYADHIuUZwEGHkHkAr46jj8GfAasRfgIzUoC7GuS+2gltD8CeI4UjjIGzXhgPDAorsUuoAjYBOwAijHYg8N+AjUDfmIEyMIgD5d+wAA0 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.GreenDark">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15fFXF2ce/z5xzb1ZCwhJAFsMiKgGpImq1WrBuICIqiaC1rnVp3y6vWrtYK9VatWoXu71t7W5dEhuV4FJRwX0BVFYBIQk7hJ0kJPee5Xn/uDcxdwkkkN3+Ph8+J8zMOWfmzu88M/PMM88jdD/IyEsY6hqOFWWYCnnAEIG+Cn2A3kBK9F969J79QCj6b4fAToRKYL2vVIhQ5ikfryumvAPa06aQjq7A4WJkAQMd4TRRTgXGA2OAHk0Ud4CdCvsFPGBfND1LwZIIIXoDgSbu3wcsRVigPm+7Nm9teILNrdicdkeXI8CISaS4PZhglEnAJGBkXJF1wBKEZepTJoYKVTZYQbas+VdDhx/4 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.GreenDarkness">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15fFXVtce/a59zb0aSMINMGRDEiEojarVYsKhMDWAhBYenohXRZ/U5Va1Wn9Sp1qfP2to+h9bWWqUWgUSKA0VFBCQ4hEFASEKYNMyQ6d4z7PfHvQl3CiSQ2f4+n3zuzd77nLP23b+zp7X2WkLHgzCIDGyGIGSiSQf6I3RH0w3oCsQF/xKD11QBvuDfHoS9QDlQhkspQjEOX7KVkpavTvNCWluAE8Yg+mBxPsJ5wHBgKNCpntIWsBdNFYIDHAqmp6AxEBIJEMRTz/WHgDXAKjQfY7OMbexsusq0PNofAQYSh81IFGOBscCgiBJbgSJgLZpihFI02zDYxea6Bj/WM1Jw6A30B9IRMoHT [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.GreenDarknessSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15eFTV+fg/59yZyb5DWBOyAAoRlQYURRQUKYsGUIjgLlhxqdW6ULV1qRSXWqu1Lm3d6lJUqmGLuCCCIoJsYghIEJIQNlkTIOvc5fz+mEnILNkgyUz6/X2eJ09mzjn33vfOee+5Z3nP+wr+9xD0JRWDfgjSUKQAyQg6o+gEJAAh7r9w9zGVQI377xCCw8ABoASLYgSFmPzITora/3baFhFoAU6ZvvRAZyiC84HBwAAgqoHSOnAYRSUCEzjmTo9GoSEIx6Ug9gaOPwZsAtai+BaDlexib+vdTPvT8RSgNyEYDEcyBhgD9PUqsRPIA/JRFCIoRrELjX1sr6vwpq4RjUk3IBlIQZAGnIFL [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.GreenDarkSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic5Z15eFTV+fg/77kzk0xIQtjCDiEoKouIgFqtCi6oqIhKIrhvdemmdWurVanWrWptrdW2Wq0batCogLgVwRUUcGETEJKw7xDIPnd5f39MEjNLIECSGb6/z/PwDDnnzL3nznnvWd9F+L+H9DufPo7hMFFyVcgBegl0UugIdABSav+l1X6nEqip/bdVYBvCZmC1p5SIUOQq36+aTHECnqdFkURXYH/pl0d3WzhOlGOB4cAgIKOR4jawTaFSwAV21aZnKlgSFogOgL+R7+8CFiLMVY8vHB+fr3mV9c34OK3OAScAB51BipPBCKOcAZwB9IsqsgpYgLBIPYrEUKLKGivAhhUv1zf47u9xEZlu [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.GreenLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z13nB3Fkce/1fPe26gNWiWUMSCCECAkAUc6kZUQkg8EInzAAlsIzofPGB+G8yGDSefjzofzYewDc2CELUC7ImMQGQWMIpKQlXNAadN7E+r+6Lerl1Z6u9oMv/2sRtvdM9MzXVNdXVVdJXQ2KAIcCRwPfA0YCPQHugPdgDIgJ/6bHz+rGojGf3cCu4DtwHpgLbAa+AxhTSs9RatB2roDhw2lD3AWcCYwAhgCdGmgtYsd3GrAB/bFy4sAB0sQZUC4gfP3AYuBecAHwPsImw//IdoOHY8AlBxgJDA6/jsopcU6YBGwBPvlrgU2AFuQ+gE/1D2KgCOwnGMglpOciCWuASmtVwAvA68AbyNE [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.GreenLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z13fBzVtfi/d2ZXvUu2XCQXirExbljGgIFnui0b2yRgMB3DL8bwCDxaKC/BgdBeHgmPUJKQkFACwRADlmyqY3pxARe5G1vuRZYtW9Kudqec3x+zkrepl129l68+Zti5sztn5p65c++5556j+N+GoICBwBDgGGAA0A/oAeQBuUBi4F9K4FsewBf4dwCoBPYD24FyYAuwDsXWLrqKLkPFWoB2I/QFxgGnA2OAYUB6I0cbOJXrASzgSGB/BqDjKEQu4G7k+0eA1cBS4CvgSxS7238RsaP7KYCQCIwHJgb+DQo7YhuwCijDeXLLgR3AHlRDhTd3jgygN07LMQCnJTkJR7n6hx29AXgPeB/4 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.GreenSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15fFTV3fC/59w7k0wSspAQDBDWAiIiIiAoagGxRUTAPoriLvoK2ro8bnVnFFF8rNVa18fW1qUuaFFIwAURd5RNCAEBEQLIGgghJJnM3OW8f9xJyCzZIMlM+r7fzyefyZxzZu65c373rL9F8J+HwEsPBP2Anii6A12BDkAWkAkkBP+Sgp+pBPzBv/3AAWAfsB0oArag8SMPsLUV76NVELGuwDHzCJ0xGQGcDgwFBgDt6iht4DRuJWABZcH0VEDDEYhMwFXH58uAtcByBN+i+AYvu5rlPmJE2xOAZ0jgICNRnAucC/QJK7ENKAAKgS1AERo7sNiNt6bB68dLKho52HQFuqPoCZyII1zd [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Orange">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nFXFte+/q/Y+pye6oQEZmlEFCaKiAmqcIo7MghHiEJ8oCmhiTIzJS2J8EnON8Sa5N3EW0WjijQbvRaUbEIcQ4yzgAKgoyEwzQzc9n7N3rffHPt30GRq6oWfv7/PpzzldVbv22qfWXrVWrVWrhHYGBeEmjsZnMMIxWPoDfRGOAroCXYC02F9m7LJyoCr2txvYA+wENgEbENbh87nMYX0zP06TQ1qagCOFzqQXPmdhOBNlBHAikF1H8yjB4JYDPsL+oBNyAIeAIboAoTqu34+yEmEp8A4eb8uTFDbi4zQ72hwD6C2kUcl5GEajjAaOS2iyEViBsAplHcoGXDbjsk0eiA34oe+Rg0dP [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.OrangeLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nB3Fde+/p7rvnX1Go30khCRWAxIySCKsNmIxaBc8o9jGfIJN3sNLvMTBCTaLWiwGYpM4+NkOie34vfgFe8gTSDMSGIFljDEGJIxAEqvRBlpGu2a5S3fXyR99ZzR3GWlmNLvz00efubequm9V169Pnao6dY4wxKCKsNKbDHIGcBKqk4ATgVHASGAEUJT5X5q5rAVIZf7vBfYBDcA2YAvwHuq8IQvu2NyHTekTSH9X4HihdfeMR4OLEC4EZgJTgYoOivtEndsChMDhTHol4BARYgQQ6+D6wwivY3kZI78j1Odlobejp9rSHxh0BNBVDxVhD1yK6mxgNnBaTpGtwGvABuA9hC2EznZi [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.OrangeLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15fFzFle+/p+7tlrq1WbK8yAu2WcxmYxabEAgEswS8Q17whCwTksx7TPZMJpNJAsHNFmASJhkyWZglyUySITEZgy3ZEAhxEsJuAwbbYEO877K8SC21uu+9dd4fLQn1IkuyJXVr3vvpo093V9W999xbv1t1qurUOcL/MKgirIxNATkTOBnVycBJwCigFhgJlHT8RzsOawOSHf8HgSbgALAD2AZsQZ03ZOE3tg7hrQwJpNACnCi0/q7xqH8JwsXALGA6UNFDcY905bYBAdDckV4JOKQJMRII9XB8M8LrWF7CyLME+owsiu0ZqHspBIYdAXTVAyXYw5ejOgeYA0zNKrIdeA1YD2xB2Ebg [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.OrangeSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15mBTV1bjfU1XdMz0rM4wMzLCvIiqi4K7BNWyyJEJcExQFNFET4+eXaAyocfuyu4toTGKiYj5UZgCXoHFfAEVEWYVhG3aYfemuqvP7o3uG6WVWZqab7/m9zzNPT997q+pU31O3zr333HOF/2MoCDfQD4ehCP1x6Qv0RjgGyAG6Akmhv5TQYVVAbehvP3AA2AtsA4oQNuOwVuazpZNvp8OReAtwpOhs8nE4C4MzUUYBJwDpjRQPEKzcKsBBKAuehAzAJKgQXQFPI8eXoXyFsBz4CJsP5VmK2/F2Op2jTgH0JpKoYTQGY1HGAoMjimwFViOsQdmMUoTFdix2ySOhCm/+GhnY9MClNy59 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Pink">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nB3Fcce/1fPe2/vSrlb3BUIcQkhCK3CMIdygRQhIjAzGfGwDicCOg+MrPuKAjTEQx4mDryTEdrAdHyIRoF0hTmNsMEZIGAlJoMO679Xq2vO9menKHzO7etdKb6W98U+f/Yxed89M93R1dXV1dZUwxKCoAJOAM4FTgInAeGA4UAVUAnnhX2F4WysQD//2A43APmAbsAXYBLwtyOY+akafQfq7AicLRccAFwDvBWYD04CSLoq7BJ3bCvjAkTC9FHAICKISiHZx/xHgLeB14HfAK4LsOvlW9B8GHQEomgdcDMwJ/6akFdkKrAJWE4zcLcB2YLcgR8gBipYCowg4x0QCTnI2AXFNSCu+ [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.PinkLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nB3Fde+/p7rvnbmzarQMEkIgVhuEDEgjwGBsxGJAgCwWDWZxgk3ew0scO8TJc0z8wDheiEMW/GzHz/HyEmJgwGKRwBgMwmaXRmKVMIvRwiLQrtlvd1Wd90ffkeYuI81Is5Pf5zOfnltVXX2q6/SpU1WnzhHGGFRVWNR8ME6ORDgEZTpwIMgk0InABKAs91eRu60DyOb+NoNsAd2Ish7RtShvIP5laTxhzXC0aTAhw03AvkKbnpuKupMRPQlkDuhMoLqX4jGwhaTDHdCSS68BAhKGmACkerm/BXgR1eUYeRJrnpBLZ70zYI0ZBow6BtD7XyujteVURM8BzgGOKCiyDvQFVF7C8AZe [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.PinkLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic5Z15fFzFle+/p+7tbnVrl+XdBi/gYIxZbBkIZMGE1YBjAhZhSYYk8x5ZJxkmk8mEyUCSyTYZJhkySSYvM0neDAkgB7NYEAJDnITdls1qg1m877u1tfoudd4f3bLViyzJltSteb/PR5+rrqp777m3freqTtWpc4T/YVBVYWnLVEKZiTANZQpwAsho0HpgFBDL/CUyp3UCqczfXpB9oLtRNiO6EWU9Yl+XxnM2FOOZhhJSbAGOF9r00kQ0PB/R80Dmgc4GKnsp7gP7SFd4CLRm0qsAhzQhRgGRXs5vBV5FdSVGniUwz8j1c7YP2sMUASOOAProWzHaWi9A9HLgcmBGTpFNoK+g8hqG9Vjd [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.PinkSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nBxVufe/T1V3T3fPvmQm+8ISCAGSkAmgEW5kERJCAC9EEHABXwG9Xr2KXpdXQZHt+nr1KqJe3HBDggbITFhFQEG2BElIQjaTyb5MJpnMTPdMdy3P+0fVJL1NZiaZme65118+86n0Oaeqnqrz1HPOec6zCP/DoKgAk4ApwHHARGA8MAKoAaqBIv8v6p8WBxL+3z6gBdgLbAWagE3AO4JsHqLHGDJIvgk4Vig6BpgNvBuYBZwGlPbQ3MLr3DjgAG1+eRlg4jFENRDs4fw24G3gDeCvwMuC7Dz2p8gfhh0DKFoEzAHm+n+TM5psAVYCq/C+3CZgG7BLkDb6AEXLgFF4kmMiniQ5FY+5 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Purple">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15mFXFmf8/b51zb/ftbnphk2ZHA0QFiYJbXKJGExdEzUTiEp8YdYImE5NJTOKC9GFRcYwzGbP6yzLJxNGIGRRo0KjEGHcBFUQERUCQrRuapre7nHOqfn+ce5u+S0M39J758vDcvlV1zq069T1vLe9b7yv0PYiDM0aQY4GjDWY0MBIYBAwEBgB5yf8FyWuagHjy/x5gL1AFbAW2AJssrPfv5u7NXdiOLoF0dwWOFPOYN8zDOwP4LHAyMBHo10pxl6BzmwAfqEumFwMWASEGAKFWrq8D3gVWCPKqwbzi4OzokIZ0E3odAR7iobx97DvHYC4CLgLGZRT5GFgDrAU2AVssrG0+/k4Hp442 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.PurpleSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nBXFufe/Vd3nzDmzDwwDw74EEAUJCi5xiQsmioqaG4m4xC1vUJOoNzHGFRpExWtMck1i9JrNuEX0osKARkOIcZdFQGQTWWUdmBlmO0t3V71/9JlxzjLMDJyZcybv++PD50xXVXdXdz391FP1bIJ/PwgLa4hAjAKGavRgYCDQCygFegI5sf+5sXMagUjs/37gALAP2A5sBTYbGOvu5d4tXfgcXQKR6Q4cKWYzu5+DcwrwNWACMAYoaKW5jTe4jYAL1MbKCwEDjyB6Ar5Wzq8FPgGWCsR7Gv2uhbUrLQ+SIXQ7AniUR3OqqT5Do88DzgNGJDTZBqwG1gCbga0Gxg4Xd7eFVUs7YGEV [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Red">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z13nF3Fke+/1efeyZqgUUIZAyIIAUIBLxhWJKNkIXlBRoQHFixCsF57DfZiszaYzPrx1ovTejG2wawNwhZIMwKJYBAZMcIoIglWGmVQDpPuPaHeH31mdNNIM6PJ+Pf5zOfM7T6hz+nq6uqq6iqhm0FBgGOBk4EvAEOBwUBvoBdQCmSHf3nhZTVALPzbBewGdgCbgEpgPfCRwIZ2eo12g3R0A44WCgOAc4CzgTHACKBHI6e72M6tAXzgQFheCDhYgigFoo1cfwBYAbwPvA28JbDt6N+i49DlCEDtyB0HTAj/hqWcshFYDqzEjtxKYDOwXQ51+JGeUQgcg+UcQ7Gc5FQscQ1JOX0t8AKw [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.RedLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z17nFXVlee/65xzb9Wtd1GAFG9QMSqIDzDaPuIzvtBopmWSmEyb2DPm0TGZtOn2gXBAjNrG7rSZJO0k6clMZ9oEe4hKgUYlxhjfYEQBRVFeQkEBBVTdqvs45+w1f5x7i7qPgiqot/l9PvW5dffe59y9z15n7bXXWnstYZhBQXDdKYicCExFdTIwERgFjATqgJLMX1nmsnYglfnbA+wFmoCtwGbgQ2z7Hbnrrk39OJR+gQx0B44WunjxOHz/HOAvgNnADKCyi+Ye4eS2AwHQkimvAmxCgqgDIl1c3wK8DbyOyEuoviiuu6NXBjJAGHIEoA89VMK+fRegegVwBTAtr8kW4C1gLfAhsBnb [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.RedLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15fBzVle+/p6q61a1dsrzI+wJmszEYmyUsYU3YQ+YFT8gygWTeIzt52YaExWUDAYYwyZDJwstMlkkYEpNxAMuGmBCSELZgAwbb2MZ4t2VblixL3eqllvP+qJYsdbcsyZbUrZn5fT76tPrWra5bdU/de+655/yO8F8MCoJtT0PkJGA6qlOBycBooA4YBZRk/kozp3UAqczfAaAZ2A/sALYBWzDNt+WOO7YO460MC6TQDThW6N13T8B1zwXeA8wHZgMVvVR3CDq3A/CAtkx5JWASCMQoINTL+W3AW8CriLyI6gti23sG5UYKhBEnAPrQQyUcPHghqlcAVwAzs6psB94E1gJbgG2Y5k48 [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.RedSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z13mFbVtf8/+5z3nd5nYOjFgiIiIMWCerFFmoCJothFf0H0mnhtsdwosXv9eZNrLEk0JpZY0KAwg4KGYC8UpQwISGDoHQamvu8p6/6xzwxvm8rMvO/k5vs885x5997nnHXOXmfvtddaey3FPxkEFNAX6A8cBfQBegGdgAIgH0j2/tK806qAgPe3F9gH7AY2A6XABuB7BRvb6THaDSreBBwpBLoDI4HTgeHAQCCznuYWunOrAAc45JVnASaaIfIBfz3nHwJWAouBL4EvFGw/8qeIHzocA4j+ckcBY7y/fhFNNgErgBL0l1sKbAF2qMMd3tg9soCu6JGjD3okORHNXL0jmq8FPgDmAR8r [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Tan">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nFXFlce/p+597/XeDU2zCCjgMnFhzKJOYmIiRmM0igjSk6hJRHB6lMXEMTPJOBk7OlmcxGjabYgsmYwTk1ZQhDCOxpjEGBfQUZEYE6MIKAo0a2/v3rp15o/3GvotDa+hV8zv8+nPe11Vt+65t36v6lTVqXOEQwyqKksX3zUeZ49FZIKi4xRzuEFrVBiGUi1CQpUEUJK+rFWEpCpJYKtAE8Jmp7Je1K3DM687q6/UXjH3jX58tF6B9LcAB4vG/7xrtLHRRxE9FZWTgYmg5V0UD4EmoBWIRGQXgKpWAB4pQlQDsS6u3wWsUXQVKr+LC09eMHPu2z36QH2MQUeAlQ0NidYyOR04B+Uc [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.TanSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nFxVlfi/5773qrp6T3dnIwtJWGTLIAoMgiggiOyBkFYhKiFxWkwIio7jDOOPVseFUQTDNhFI1EHRhgRCAiKIoIAsCQgkMWxmD1k7Wy9V9ZZ7fn9Ud+juqk66k+6uysx8P5/+VNe9991337un7nruOcL/MFRV5s+9ayw2PBqRcYqOUcxogw5WoQalWoS4KnGguO2yVhHSqqSBbQKNCFusylpRuxrHrLShrqj94rWr8vho/YLkuwAHSsMv7hphwug0RE9F5SRgPGhZN8kDoBFoBSIR2Q2gquWAQ0YgqgGvm+t3A0sVXYzKX2LC85dMvfa9Pn2gAeagE4DHZs2Kt5bKGcB5KOcBR3ZJ [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Turquoise">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15mFbFlf8/p+59317phU0aaBYNEBUkKm5xiRhNFBExE4lLfGLUCZpMTCYxiQvSl0XFMc5kzOovyyQTRyNmUGhAoxJj3AVUEBEUAUG2bmia3t7l3ls1f9z3bfpdGrqh9/y+z9PP229VvfdW3Tr31Dl1Tp0j9D0IjjMakeOBYzFmFDACGAQMBAYAOYm//MRvmoBY4m8vsA+oArYBW4HNWNb73H33li4cR5dAursDR43584fheWcDnwVOAyYA/Vpp7RJMbhPgA3WJ8iLAIiCIAUCold/XAe8CKxF5FWNewXF2dsg4ugm9jwAeeiiH/fvPx5hLgEuAsWktPgbWAuuAzcBWLGs7vr8Lx6mj [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.TurquoiseSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15fFTV3f/f5947k5nsgbCEsFNAloggKK4FxVYRUfsoFZe69VfU1uVRa13JZVHxsVYf6/rY2lq3ihaVAFqV4goqi4DIJjuyBpKQZDLLXc7zx50JmSUkgUlm0uf3eb3ymsm55849957vPef7Pef7/XwF/34Q6HofhBgE9EXK3kBPoBNQCHQEMsJ/meFz6oBg+O8AcBDYD+wAtgFbUNV1PPDA1ja8jzaBSHUDjhkzZxZjmqcBpwKjgBIgp5HaBk7n1gEWUB0uzwVUHIHoCLgaOb8a+BZYihCLkfILdH13Uu4jRWh/AvDkkxlUVo5ByvOA84ABMTW2A6uBNcAWYBuquhPL2oOuV9Mc6Hou [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.White">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7X1/fFTllff33HtnMpNJJpkkJAHCjyhGAZEACW93UQuIChERdwtra/3U1r4va91uW9fu267bVWvrj/V127Vvf9f+2r5bxV0UEkBQq1atFYOFQkB+CCT8yG8IyWSSub/O+8dzJ8zcuSGTMJPMBL6fTz538tzn3vvc+5znPOc55zznEMYZmJkAlAOYCeAyANMBTAUwAUARgEIAWdZftnVZCEDY+usA0AmgDUATgGMAjgDYT0RHR+k1Rg001g24UDDzZACLAPwlgGoAcwDkDlJdg+jcEAADQLdV7gcgQxBEIQDXINd3A9gD4H0AfwDwDhGduvC3GDtkHAEwcxaAxQBWWH8VtiqNAP4MYC/E [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.WhiteSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7X15fBzFlf/3dfeMZkaakUaSZdmSL2yE71N2sjEGYwy2hW2OIJaEwHLk9zPnmgTvJoTNAjHh2OANCzGBBBKShUDMri8JjM1h7sMXOFgyPrAt+ZBkSbYszYw008fbP6pHnkvS6JoZJfl+Pvr0qKq6+3XX66pXr95B+CsDMxOAUQDGATgHwEgAwwEMApALIAdAmvnnME/zAfCbfw0AGgGcBFAN4AiAQwD2EtHhBD1GwkDJJqC3YOYCALMBfAvATACTADg7aK5CdK4PgA6g2Sx3AZAhGCIHgKWD85sBfAlgO4CPAXxERCd6/xTJw4BjAGZOAzAXwCLzryiiSRWAvwDYA/HlHgFwFEANETUj [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.Yellow">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nFbFlfe/597neXrf6AaaHRSJiAhIo5kYHdwiW1BnIqMxvknUCZpMJpnEZJI4GU1MXCav7yRmn+zLJBFnUOlGxSVq1BgRjCCgLJGdBpq91+e599Z5/6jbzbM1dDe9m9/n05/qp6ruvVW3zj1Vdc6pc4RBBlUEmABMBk4DxgNjgaFABVAO5IR/+eFlTUA8/DsAHAT2AzuAbcDbwJsibO2lbvQapK8bcKpQZRRwAfAeYBYwFShqp7qHHdwmIACOhfnFgIsliHIg2s71x4A3gFeBPwIvibDn1HvRdxhwBKBKDjAbmBv+TUqrsh1YC6zDfrnbgJ1ArUjbgJ/sGcXACCznGI/lJGdjiWtc [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.YellowDark">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15mBzFef8/b3XP7Mzeq/sEcYlDCCHtLmAwGHEYJEAIB2TOxxiIATuOHYwd28QBQ2xMHBIHx0d+jq+YgJEdAdrlPgTmlnYFEpJAgHWi+9aeM91d7++Pnl3tHCvtSrOn830eqXeqqrvf6nrrrXqr3npfYZBBFaGOIxCOB44EJgCHIQxHGQYMBQpS/wpTtzUDidS/7Qg7ULYC61DWAKuwvCensrqXq9PjkL4m4FCh7zCWgDNQTkeoRpkMlHRS3AN2EDZ4AOxNpZcCDiFDDAUindy/F3gXZRHC6xhek2lszFtl+gADjgH0QwrYy9koM4AZwMSMImtRliIsA1alevB6AjbJae0Nvv93vEkp [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.YellowDarkSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nFxFtfi/p24v0z17ZrLvLGEJIdsMWwQTFoEAAXwQQUAR/Am4PH2IPpenIAjC86E+VNSn4oYgUQMkYTcGhLAkk5AdAjEr2ffZu++9dX5/dM9kepktmZnuvN/v+/kk3VNV996qrnNrPXWO8L8MVYQaRiOcBBwDjAJGIPRHqQQqgHDyXzR5WSMQS/7bg7AXZRewGWUjsB7LO3I6G/q4OL2O5DoDR4ouYyg+U1DOQqhGGQcUt5PcBfaSqHAfqE2GlwAOCYGoAILtXF8LrERZjPA6hoUyiW09VpgccNQJgL5PmFqmolwMXAyMSUuyCWUFwipgffIN3oLPdjmjtcI7fsablOAwGBiBYRTK [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.YellowLight">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15nF1Vle+/a59zb83DrapUEkImCGiYkaAIooxCIiAgqQjIa9R+D4e2tWm7ny3tA6EdaNse8LW2z3boblqgomFIMQgiIjIICYKQMJoRMtRcqfGec/Ze749zK6k7VFIVatbf51Ofc2vvfc5Z5+x11l577bXXEmYYVFXoWrMYZ5cichjoImAByiygDqgFijJ/pZnT+oB05q8VaENoBt2GyhbQTRh5SaobNk/4A40zZLIJeKvQ1sZ5iJ4GcirCycCxQMUwzUOgDehDsQh74otQieARM0QtkBjm/D3AC6g+A+YJXPS4zLp8x5g+0ARj2jGA6n1FtHefAbIcw3KUI3OabAV+B/oiajahbgsq [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.YellowLightSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15mFxVmfB/77m3qruq16rudGdfIETCGiBxkLiwLxEwKGlkc1Dn+3AdHcZxnGH8QBi3cRwdnNFhFnVmUKAjYUlYhEFUBGQJihAggNn3XqrTS3VV3XvP+/1R1UnX0kl30t1V8Znf89RTVeece++597z3rO95X+EPDFUV9q6ahw0WInIU6FxgNsoUoBloAqpyn2jusCSQzn06gS6EPaBbUNkEugEjr0lj28ZJv6EJRsqdgcNFO9tnILoU5AyEJcCJQN0IyT2gC0iiBAi92ZNQj+CQFYgmIDTC8b3Ay6g+D+ZprP+UTLlyx7je0CRzxAmA6kNVdPedCXIRhotQFhQk2Qz8DvQV1GxA7SZU [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:LaavorCheckBoxRadio.TileRadio.YellowSelected">
            <summary>
              Looks up a localized string similar to iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAHjQAAB40BBo7frwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACAASURBVHic7Z15fBzVle+/t6q61d3aF1uyLW9gDMYY21hmiYExuzcMZMCBAAlL3rBkMskQwpDwEkjYh8ckQwgkQ0ICIQsmMWDJgCEECFswNmBjG2+xJW+yLcuSpe6Wums5749qyepF+9KtTH6fjz6lvvdW3XPrnrrLuWdR/J1BBAVMBKYARwETgHHACKAEKAayYn+B2G1hIBL7OwjUAweAnUA1sB34TCl2DFEzhgwq3QT0FyKMAeYAnwNmA9OA3E6Km7idGwZsoCmWngfouAxRDHg6ub8J+BT4EHgPeFcp9va/FenDsGMAEbKAucD82N/khCI1wDpgPe6XWw3sAmqVau/w7urIA0bhjhwTcEeSE3CZ [rest of string was truncated]&quot;;.
            </summary>
        </member>
    </members>
</doc>
